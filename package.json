{"name": "site-inspection-system", "version": "1.0.0", "description": "站址巡检系统 - 智能安全帽语音交互后台管理系统", "main": "index.js", "scripts": {"dev": "concurrently \"npm run server:dev\" \"npm run client:dev\"", "server:dev": "cd server && npm run dev", "client:dev": "cd client && npm run dev", "build": "npm run client:build && npm run server:build", "client:build": "cd client && npm run build", "server:build": "cd server && npm run build", "start": "cd server && npm start", "install:all": "npm install && cd server && npm install && cd ../client && npm install", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down"}, "keywords": ["inspection", "smart-helmet", "voice-interaction", "management-system"], "author": "Your Name", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}}