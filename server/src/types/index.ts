import { Request } from 'express';

// 用户相关类型
export interface User {
  id: number;
  employee_id: string;
  name: string;
  role: UserRole;
  department?: string;
  email?: string;
  phone?: string;
  status: UserStatus;
  created_at: Date;
  updated_at: Date;
}

export enum UserRole {
  SUPER_ADMIN = 'super_admin',
  SITE_ADMIN = 'site_admin',
  INSPECTOR = 'inspector',
  VIEWER = 'viewer',
}

export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended',
}

// 设备相关类型
export interface Device {
  id: number;
  device_id: string;
  device_name: string;
  device_type: string;
  status: DeviceStatus;
  bound_user_id?: number;
  firmware_version?: string;
  battery_level?: number;
  last_heartbeat?: Date;
  created_at: Date;
  updated_at: Date;
}

export enum DeviceStatus {
  ONLINE = 'online',
  OFFLINE = 'offline',
  MAINTENANCE = 'maintenance',
  ERROR = 'error',
}

// 站址相关类型
export interface Site {
  id: number;
  site_code: string;
  site_name: string;
  address: string;
  latitude?: number;
  longitude?: number;
  site_type: string;
  site_level: string;
  description?: string;
  created_at: Date;
  updated_at: Date;
}

// 巡检任务相关类型
export interface InspectionTask {
  id: number;
  task_code: string;
  site_id: number;
  assigned_user_id: number;
  template_id: number;
  status: TaskStatus;
  scheduled_date: Date;
  started_at?: Date;
  completed_at?: Date;
  notes?: string;
  created_at: Date;
  updated_at: Date;
}

export enum TaskStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
  OVERDUE = 'overdue',
}

// 巡检记录相关类型
export interface InspectionRecord {
  id: number;
  task_id: number;
  item_id: number;
  step_number: number;
  result_data: any;
  voice_text?: string;
  audio_file_path?: string;
  status: RecordStatus;
  created_at: Date;
  updated_at: Date;
}

export enum RecordStatus {
  NORMAL = 'normal',
  ABNORMAL = 'abnormal',
  SKIPPED = 'skipped',
}

// 异常相关类型
export interface Exception {
  id: number;
  task_id: number;
  record_id?: number;
  exception_type: string;
  severity: ExceptionSeverity;
  description: string;
  location?: string;
  status: ExceptionStatus;
  reported_by: number;
  assigned_to?: number;
  resolved_at?: Date;
  created_at: Date;
  updated_at: Date;
}

export enum ExceptionSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

export enum ExceptionStatus {
  OPEN = 'open',
  IN_PROGRESS = 'in_progress',
  RESOLVED = 'resolved',
  CLOSED = 'closed',
}

// API 响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  error?: string;
  timestamp: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    pageSize: number;
    total: number;
    totalPages: number;
  };
}

// 请求类型扩展
export interface AuthenticatedRequest extends Request {
  user?: User;
}

// 查询参数类型
export interface QueryParams {
  page?: number;
  pageSize?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  search?: string;
  filters?: Record<string, any>;
}

// MQTT 消息类型
export interface MqttMessage {
  deviceId: string;
  messageType: string;
  payload: any;
  timestamp: Date;
}

// WebSocket 消息类型
export interface WebSocketMessage {
  type: string;
  payload: any;
  timestamp: Date;
}
