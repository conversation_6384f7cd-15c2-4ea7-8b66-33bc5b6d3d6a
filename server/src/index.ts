import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import compression from 'compression';
import { createServer } from 'http';
import { Server as SocketIOServer } from 'socket.io';
import rateLimit from 'express-rate-limit';

import { config } from './config';
import { logger } from './utils/logger';
import { errorHandler } from './middleware/errorHandler';
import { notFoundHandler } from './middleware/notFoundHandler';
import { initializeDatabase } from './utils/database';
import { initializeRedis } from './utils/redis';
import { initializeMinio } from './utils/minio';
import { initializeMqtt } from './utils/mqtt';

// 路由导入
import authRoutes from './routes/auth';
import userRoutes from './routes/users';
import deviceRoutes from './routes/devices';
import siteRoutes from './routes/sites';
import templateRoutes from './routes/templates';
import taskRoutes from './routes/tasks';
import uploadRoutes from './routes/upload';
import statisticsRoutes from './routes/statistics';

class Application {
  public app: express.Application;
  public server: any;
  public io: SocketIOServer;

  constructor() {
    this.app = express();
    this.server = createServer(this.app);
    this.io = new SocketIOServer(this.server, {
      cors: {
        origin: "*",
        methods: ["GET", "POST"]
      }
    });
    
    this.initializeMiddlewares();
    this.initializeRoutes();
    this.initializeErrorHandling();
    this.initializeSocketIO();
  }

  private initializeMiddlewares(): void {
    // 安全中间件
    this.app.use(helmet());
    
    // CORS 配置
    this.app.use(cors({
      origin: config.server.env === 'production' 
        ? ['http://localhost:3000', 'http://localhost:80'] 
        : true,
      credentials: true,
    }));

    // 压缩响应
    this.app.use(compression());

    // 请求日志
    this.app.use(morgan('combined', {
      stream: { write: (message) => logger.info(message.trim()) }
    }));

    // 请求解析
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // 速率限制
    const limiter = rateLimit({
      windowMs: config.security.rateLimitWindowMs,
      max: config.security.rateLimitMaxRequests,
      message: {
        success: false,
        message: '请求过于频繁，请稍后再试',
        error: 'RATE_LIMIT_EXCEEDED'
      }
    });
    this.app.use('/api', limiter);

    // 健康检查
    this.app.get('/health', (req, res) => {
      res.json({
        success: true,
        message: '服务运行正常',
        timestamp: new Date().toISOString(),
        version: '1.0.0'
      });
    });
  }

  private initializeRoutes(): void {
    // API 路由
    this.app.use('/api/auth', authRoutes);
    this.app.use('/api/users', userRoutes);
    this.app.use('/api/devices', deviceRoutes);
    this.app.use('/api/sites', siteRoutes);
    this.app.use('/api/templates', templateRoutes);
    this.app.use('/api/tasks', taskRoutes);
    this.app.use('/api/upload', uploadRoutes);
    this.app.use('/api/statistics', statisticsRoutes);

    // API 文档
    this.app.get('/api', (req, res) => {
      res.json({
        success: true,
        message: '站址巡检系统 API',
        version: '1.0.0',
        endpoints: {
          auth: '/api/auth',
          users: '/api/users',
          devices: '/api/devices',
          sites: '/api/sites',
          templates: '/api/templates',
          tasks: '/api/tasks',
          upload: '/api/upload',
          statistics: '/api/statistics',
        }
      });
    });
  }

  private initializeErrorHandling(): void {
    // 404 处理
    this.app.use(notFoundHandler);
    
    // 错误处理
    this.app.use(errorHandler);
  }

  private initializeSocketIO(): void {
    this.io.on('connection', (socket) => {
      logger.info(`WebSocket 客户端连接: ${socket.id}`);

      socket.on('join_room', (room: string) => {
        socket.join(room);
        logger.info(`客户端 ${socket.id} 加入房间: ${room}`);
      });

      socket.on('leave_room', (room: string) => {
        socket.leave(room);
        logger.info(`客户端 ${socket.id} 离开房间: ${room}`);
      });

      socket.on('disconnect', () => {
        logger.info(`WebSocket 客户端断开连接: ${socket.id}`);
      });
    });
  }

  public async initialize(): Promise<void> {
    try {
      // 初始化数据库
      await initializeDatabase();
      logger.info('数据库初始化完成');

      // 初始化 Redis
      await initializeRedis();
      logger.info('Redis 初始化完成');

      // 初始化 MinIO
      await initializeMinio();
      logger.info('MinIO 初始化完成');

      // 初始化 MQTT
      await initializeMqtt(this.io);
      logger.info('MQTT 初始化完成');

    } catch (error) {
      logger.error('应用初始化失败:', error);
      throw error;
    }
  }

  public start(): void {
    this.server.listen(config.server.port, config.server.host, () => {
      logger.info(`服务器启动成功: http://${config.server.host}:${config.server.port}`);
      logger.info(`环境: ${config.server.env}`);
    });
  }
}

// 启动应用
const app = new Application();

app.initialize()
  .then(() => {
    app.start();
  })
  .catch((error) => {
    logger.error('应用启动失败:', error);
    process.exit(1);
  });

// 优雅关闭
process.on('SIGTERM', () => {
  logger.info('收到 SIGTERM 信号，正在关闭服务器...');
  app.server.close(() => {
    logger.info('服务器已关闭');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  logger.info('收到 SIGINT 信号，正在关闭服务器...');
  app.server.close(() => {
    logger.info('服务器已关闭');
    process.exit(0);
  });
});

export default app;
