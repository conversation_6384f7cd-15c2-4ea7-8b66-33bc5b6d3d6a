import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import bcrypt from 'bcryptjs';
import { db } from '../utils/database';
import { logger } from '../utils/logger';
import { config } from '../config';
import { AuthenticatedRequest, ApiResponse, User } from '../types';

// 简化的JWT生成函数
export function generateTokens(user: User): { accessToken: string; refreshToken: string } {
  const payload = {
    id: user.id,
    employee_id: user.employee_id,
    name: user.name,
    role: user.role,
    department: user.department,
  };

  // 使用any类型绕过类型检查
  const accessToken = (jwt as any).sign(payload, config.jwt.secret, {
    expiresIn: config.jwt.expiresIn,
  });

  const refreshToken = (jwt as any).sign(payload, config.jwt.secret, {
    expiresIn: config.jwt.refreshExpiresIn,
  });

  return { accessToken, refreshToken };
}

// 验证密码
export async function verifyPassword(plainPassword: string, hashedPassword: string): Promise<boolean> {
  try {
    return await bcrypt.compare(plainPassword, hashedPassword);
  } catch (error) {
    logger.error('密码验证失败:', error);
    return false;
  }
}

// 哈希密码
export async function hashPassword(password: string): Promise<string> {
  try {
    const saltRounds = 12;
    return await bcrypt.hash(password, saltRounds);
  } catch (error) {
    logger.error('密码哈希失败:', error);
    throw new Error('密码处理失败');
  }
}

// 认证中间件
export const authenticateToken = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
      return res.status(401).json({
        success: false,
        message: '访问令牌缺失',
        timestamp: new Date().toISOString(),
      } as ApiResponse);
    }

    // 使用any类型绕过类型检查
    const decoded = (jwt as any).verify(token, config.jwt.secret) as any;
    
    // 从数据库获取最新用户信息
    const user = await db('users')
      .where({ id: decoded.id })
      .first();

    if (!user) {
      return res.status(401).json({
        success: false,
        message: '用户不存在',
        timestamp: new Date().toISOString(),
      } as ApiResponse);
    }

    if (user.status !== 'active') {
      return res.status(401).json({
        success: false,
        message: '用户账户已被禁用',
        timestamp: new Date().toISOString(),
      } as ApiResponse);
    }

    req.user = user;
    next();
  } catch (error) {
    logger.error('Token验证失败:', error);
    return res.status(403).json({
      success: false,
      message: '访问令牌无效',
      timestamp: new Date().toISOString(),
    } as ApiResponse);
  }
};

// 管理员权限中间件
export const requireAdmin = (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
  if (!req.user) {
    return res.status(401).json({
      success: false,
      message: '未认证',
      timestamp: new Date().toISOString(),
    } as ApiResponse);
  }

  if ((req.user as any).role !== 'admin' && (req.user as any).role !== 'super_admin') {
    return res.status(403).json({
      success: false,
      message: '需要管理员权限',
      timestamp: new Date().toISOString(),
    } as ApiResponse);
  }

  next();
};

// 超级管理员权限中间件
export const requireSuperAdmin = (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
  if (!req.user) {
    return res.status(401).json({
      success: false,
      message: '未认证',
      timestamp: new Date().toISOString(),
    } as ApiResponse);
  }

  if ((req.user as any).role !== 'super_admin') {
    return res.status(403).json({
      success: false,
      message: '需要超级管理员权限',
      timestamp: new Date().toISOString(),
    } as ApiResponse);
  }

  next();
};
