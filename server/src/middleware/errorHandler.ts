import { Request, Response, NextFunction } from 'express';
import { logger } from '../utils/logger';
import { ApiResponse } from '../types';

// 自定义错误类
export class AppError extends Error {
  public statusCode: number;
  public isOperational: boolean;
  public errorCode?: string;

  constructor(
    message: string,
    statusCode: number = 500,
    errorCode?: string,
    isOperational: boolean = true
  ) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = isOperational;
    this.errorCode = errorCode;

    Error.captureStackTrace(this, this.constructor);
  }
}

// 常见错误类型
export class ValidationError extends AppError {
  constructor(message: string = '数据验证失败') {
    super(message, 400, 'VALIDATION_ERROR');
  }
}

export class AuthenticationError extends AppError {
  constructor(message: string = '身份验证失败') {
    super(message, 401, 'AUTHENTICATION_ERROR');
  }
}

export class AuthorizationError extends AppError {
  constructor(message: string = '权限不足') {
    super(message, 403, 'AUTHORIZATION_ERROR');
  }
}

export class NotFoundError extends AppError {
  constructor(message: string = '资源未找到') {
    super(message, 404, 'NOT_FOUND_ERROR');
  }
}

export class ConflictError extends AppError {
  constructor(message: string = '资源冲突') {
    super(message, 409, 'CONFLICT_ERROR');
  }
}

export class DatabaseError extends AppError {
  constructor(message: string = '数据库操作失败') {
    super(message, 500, 'DATABASE_ERROR');
  }
}

// 错误处理中间件
export const errorHandler = (
  error: Error,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  let statusCode = 500;
  let message = '服务器内部错误';
  let errorCode = 'INTERNAL_SERVER_ERROR';

  // 处理自定义错误
  if (error instanceof AppError) {
    statusCode = error.statusCode;
    message = error.message;
    errorCode = error.errorCode || 'APP_ERROR';
  }
  // 处理数据库错误
  else if (error.name === 'ValidationError') {
    statusCode = 400;
    message = '数据验证失败';
    errorCode = 'VALIDATION_ERROR';
  }
  // 处理 JWT 错误
  else if (error.name === 'JsonWebTokenError') {
    statusCode = 401;
    message = '无效的访问令牌';
    errorCode = 'INVALID_TOKEN';
  }
  else if (error.name === 'TokenExpiredError') {
    statusCode = 401;
    message = '访问令牌已过期';
    errorCode = 'TOKEN_EXPIRED';
  }
  // 处理 Knex 数据库错误
  else if (error.message.includes('duplicate key')) {
    statusCode = 409;
    message = '数据已存在';
    errorCode = 'DUPLICATE_KEY';
  }
  else if (error.message.includes('foreign key')) {
    statusCode = 400;
    message = '关联数据不存在';
    errorCode = 'FOREIGN_KEY_ERROR';
  }
  // 处理文件上传错误
  else if (error.message.includes('File too large')) {
    statusCode = 413;
    message = '文件大小超出限制';
    errorCode = 'FILE_TOO_LARGE';
  }
  else if (error.message.includes('Unexpected field')) {
    statusCode = 400;
    message = '不支持的文件字段';
    errorCode = 'INVALID_FILE_FIELD';
  }

  // 记录错误日志
  if (statusCode >= 500) {
    logger.error('服务器错误:', {
      error: error.message,
      stack: error.stack,
      url: req.url,
      method: req.method,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    });
  } else {
    logger.warn('客户端错误:', {
      error: error.message,
      url: req.url,
      method: req.method,
      ip: req.ip,
    });
  }

  // 构建错误响应
  const errorResponse: ApiResponse = {
    success: false,
    message,
    error: errorCode,
    timestamp: new Date().toISOString(),
  };

  // 开发环境下返回详细错误信息
  if (process.env.NODE_ENV === 'development') {
    errorResponse.data = {
      stack: error.stack,
      originalMessage: error.message,
    };
  }

  res.status(statusCode).json(errorResponse);
};

// 异步错误处理包装器
export const asyncHandler = (
  fn: (req: Request, res: Response, next: NextFunction) => Promise<any>
) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

// 404 错误处理
export const notFoundHandler = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  const error = new NotFoundError(`路由 ${req.originalUrl} 未找到`);
  next(error);
};
