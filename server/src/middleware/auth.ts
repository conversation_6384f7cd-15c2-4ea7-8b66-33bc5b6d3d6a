import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { config } from '../config';
import { AuthenticatedRequest, User, UserRole } from '../types';
import { AuthenticationError, AuthorizationError } from './errorHandler';
import { db } from '../utils/database';
import { logger } from '../utils/logger';

// JWT 令牌验证中间件
export const authenticateToken = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      throw new AuthenticationError('访问令牌缺失');
    }

    // 验证 JWT 令牌
    const decoded = jwt.verify(token, config.jwt.secret) as any;
    
    // 从数据库获取用户信息
    const user = await db('users')
      .where({ id: decoded.userId, status: 'active' })
      .first();

    if (!user) {
      throw new AuthenticationError('用户不存在或已被禁用');
    }

    // 将用户信息添加到请求对象
    req.user = user;
    next();
  } catch (error) {
    if (error instanceof jwt.JsonWebTokenError) {
      next(new AuthenticationError('无效的访问令牌'));
    } else if (error instanceof jwt.TokenExpiredError) {
      next(new AuthenticationError('访问令牌已过期'));
    } else {
      next(error);
    }
  }
};

// 角色权限验证中间件
export const requireRole = (roles: UserRole | UserRole[]) => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
    if (!req.user) {
      return next(new AuthenticationError('用户未认证'));
    }

    const userRole = req.user.role;
    const allowedRoles = Array.isArray(roles) ? roles : [roles];

    if (!allowedRoles.includes(userRole)) {
      logger.warn('权限不足:', {
        userId: req.user.id,
        userRole,
        requiredRoles: allowedRoles,
        url: req.url,
        method: req.method,
      });
      return next(new AuthorizationError('权限不足'));
    }

    next();
  };
};

// 管理员权限验证
export const requireAdmin = requireRole([UserRole.SUPER_ADMIN, UserRole.SITE_ADMIN]);

// 超级管理员权限验证
export const requireSuperAdmin = requireRole(UserRole.SUPER_ADMIN);

// 检查用户是否可以访问特定资源
export const checkResourceAccess = (resourceType: string) => {
  return async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        return next(new AuthenticationError('用户未认证'));
      }

      const userId = req.user.id;
      const userRole = req.user.role;
      const resourceId = req.params.id;

      // 超级管理员可以访问所有资源
      if (userRole === UserRole.SUPER_ADMIN) {
        return next();
      }

      // 根据资源类型检查访问权限
      let hasAccess = false;

      switch (resourceType) {
        case 'user':
          // 用户只能访问自己的信息，管理员可以访问所有用户
          hasAccess = userRole === UserRole.SITE_ADMIN || userId.toString() === resourceId;
          break;

        case 'device':
          // 检查设备是否绑定到当前用户
          if (userRole === UserRole.SITE_ADMIN) {
            hasAccess = true;
          } else {
            const device = await db('devices').where({ id: resourceId }).first();
            hasAccess = device && device.bound_user_id === userId;
          }
          break;

        case 'task':
          // 检查任务是否分配给当前用户
          if (userRole === UserRole.SITE_ADMIN) {
            hasAccess = true;
          } else {
            const task = await db('inspection_tasks').where({ id: resourceId }).first();
            hasAccess = task && task.assigned_user_id === userId;
          }
          break;

        case 'site':
          // 站点管理员和巡检员可以访问相关站点
          hasAccess = [UserRole.SITE_ADMIN, UserRole.INSPECTOR].includes(userRole);
          break;

        default:
          hasAccess = false;
      }

      if (!hasAccess) {
        logger.warn('资源访问被拒绝:', {
          userId,
          userRole,
          resourceType,
          resourceId,
          url: req.url,
          method: req.method,
        });
        return next(new AuthorizationError('无权访问该资源'));
      }

      next();
    } catch (error) {
      next(error);
    }
  };
};

// 生成 JWT 令牌
export const generateTokens = (user: User) => {
  const payload = {
    userId: user.id,
    employeeId: user.employee_id,
    role: user.role,
  };

  const accessToken = jwt.sign(payload, config.jwt.secret, {
    expiresIn: config.jwt.expiresIn,
  });

  const refreshToken = jwt.sign(payload, config.jwt.secret, {
    expiresIn: config.jwt.refreshExpiresIn,
  });

  return { accessToken, refreshToken };
};

// 验证刷新令牌
export const verifyRefreshToken = (token: string) => {
  try {
    return jwt.verify(token, config.jwt.secret) as any;
  } catch (error) {
    throw new AuthenticationError('无效的刷新令牌');
  }
};
