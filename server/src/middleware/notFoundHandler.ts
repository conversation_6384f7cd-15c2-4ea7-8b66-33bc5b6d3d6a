import { Request, Response, NextFunction } from 'express';
import { ApiResponse } from '../types';

export const notFoundHandler = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  const response: ApiResponse = {
    success: false,
    message: `路由 ${req.originalUrl} 未找到`,
    error: 'NOT_FOUND',
    timestamp: new Date().toISOString(),
  };

  res.status(404).json(response);
};
