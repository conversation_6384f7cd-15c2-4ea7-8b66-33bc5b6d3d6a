import { Router, Response } from 'express';
import { query, body, validationResult } from 'express-validator';
import { db } from '../utils/database';
import { logger } from '../utils/logger';
import { authenticateToken, requireAdmin } from '../middleware/simple-auth';
import {
  asyncHandler,
  ValidationError,
  NotFoundError
} from '../middleware/errorHandler';
import { AuthenticatedRequest, ApiResponse } from '../types';

const router = Router();

// 获取模板列表
router.get('/',
  authenticateToken,
  [
    query('page').optional().isInt({ min: 1 }).withMessage('页码必须是正整数'),
    query('pageSize').optional().isInt({ min: 1, max: 100 }).withMessage('每页大小必须在1-100之间'),
    query('search').optional().isString().withMessage('搜索关键词必须是字符串'),
    query('site_type').optional().isString().withMessage('站址类型必须是字符串'),
    query('status').optional().isIn(['active', 'inactive', 'draft']).withMessage('状态值无效'),
    query('sortBy').optional().isString().withMessage('排序字段必须是字符串'),
    query('sortOrder').optional().isIn(['asc', 'desc']).withMessage('排序方向必须是asc或desc'),
  ],
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    // 验证输入
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError('输入数据验证失败');
    }

    const {
      page = 1,
      pageSize = 20,
      sortBy = 'created_at',
      sortOrder = 'desc',
      search,
      site_type,
      status
    } = req.query;

    // 构建查询
    let query = db('inspection_templates as t')
      .leftJoin('users as u', 't.created_by', 'u.id')
      .select(
        't.*',
        'u.name as created_by_name',
        db.raw('(SELECT COUNT(*) FROM inspection_items WHERE template_id = t.id) as check_items_count')
      );

    // 搜索条件
    if (search) {
      query = query.where(function() {
        this.where('t.template_name', 'like', `%${search}%`)
          .orWhere('t.template_code', 'like', `%${search}%`)
          .orWhere('t.description', 'like', `%${search}%`);
      });
    }

    // 筛选条件
    if (site_type) {
      query = query.where('t.site_type', site_type);
    }
    if (status) {
      query = query.where('t.status', status);
    }

    // 获取总数
    const totalQuery = query.clone();
    const [{ count }] = await totalQuery.count('t.id as count');
    const total = parseInt(count as string);

    // 排序和分页
    const validSortFields = [
      'template_name', 'template_code', 'site_type', 'status', 'version', 'created_at', 'updated_at'
    ];

    const sortField = validSortFields.includes(sortBy as string) ? `t.${sortBy}` : 't.created_at';
    query = query.orderBy(sortField, sortOrder as 'asc' | 'desc');

    const offset = (Number(page) - 1) * Number(pageSize);
    query = query.limit(Number(pageSize)).offset(offset);

    const templates = await query;

    const response: ApiResponse = {
      success: true,
      message: '获取模板列表成功',
      data: {
        templates,
        pagination: {
          page: Number(page),
          pageSize: Number(pageSize),
          total,
          totalPages: Math.ceil(total / Number(pageSize)),
        },
      },
      timestamp: new Date().toISOString(),
    };

    res.json(response);
  })
);

// 获取模板详情
router.get('/:id',
  authenticateToken,
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { id } = req.params;

    const template = await db('inspection_templates as t')
      .leftJoin('users as u', 't.created_by', 'u.id')
      .select('t.*', 'u.name as created_by_name')
      .where('t.id', id)
      .first();

    if (!template) {
      throw new NotFoundError('模板不存在');
    }

    // 获取检查项目
    const checkItems = await db('inspection_items')
      .where('template_id', id)
      .orderBy('step_number');

    const response: ApiResponse = {
      success: true,
      message: '获取模板详情成功',
      data: {
        template: {
          ...template,
          check_items: checkItems
        }
      },
      timestamp: new Date().toISOString(),
    };

    res.json(response);
  })
);

// 创建模板
router.post('/',
  authenticateToken,
  requireAdmin,
  [
    body('template_name').notEmpty().withMessage('模板名称不能为空')
      .isLength({ max: 200 }).withMessage('模板名称长度不能超过200个字符'),
    body('template_code').notEmpty().withMessage('模板编号不能为空')
      .isLength({ max: 50 }).withMessage('模板编号长度不能超过50个字符'),
    body('site_type').notEmpty().withMessage('站址类型不能为空'),
    body('description').optional().isString().withMessage('描述必须是字符串'),
    body('version').optional().isString().withMessage('版本号必须是字符串'),
    body('status').optional().isIn(['active', 'inactive', 'draft']).withMessage('状态值无效'),
    body('check_items').optional().isArray().withMessage('检查项目必须是数组'),
  ],
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    // 验证输入
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError('输入数据验证失败');
    }

    const {
      template_name,
      template_code,
      site_type,
      description,
      version = '1.0',
      status = 'draft',
      check_items = []
    } = req.body;

    // 检查模板编号是否已存在
    const existingTemplate = await db('inspection_templates')
      .where('template_code', template_code)
      .first();

    if (existingTemplate) {
      throw new ValidationError('模板编号已存在');
    }

    // 开始事务
    const result = await db.transaction(async (trx) => {
      // 创建模板
      const [templateId] = await trx('inspection_templates').insert({
        template_name,
        template_code,
        site_type,
        description,
        version,
        status,
        created_by: req.user?.id,
        created_at: new Date(),
        updated_at: new Date()
      });

      // 创建检查项目
      if (check_items.length > 0) {
        const itemsToInsert = check_items.map((item: any, index: number) => ({
          template_id: templateId,
          step_number: index + 1,
          item_name: item.item_name,
          item_description: item.item_description,
          check_standard: item.check_standard,
          operation_guide: item.operation_guide,
          validation_rules: item.validation_rules ? JSON.stringify(item.validation_rules) : null,
          require_photo: item.require_photo || false,
          require_video: item.require_video || false,
          auto_capture: item.auto_capture || false,
          voice_prompts: item.voice_prompts ? JSON.stringify(item.voice_prompts) : null,
          exception_keywords: item.exception_keywords ? JSON.stringify(item.exception_keywords) : null,
          data_type: item.data_type || 'text',
          options: item.options ? JSON.stringify(item.options) : null,
          is_required: item.is_required !== false,
          created_at: new Date(),
          updated_at: new Date()
        }));

        await trx('inspection_items').insert(itemsToInsert);
      }

      return templateId;
    });

    logger.info('模板创建成功:', {
      templateId: result,
      template_code,
      created_by: req.user?.name,
    });

    const response: ApiResponse = {
      success: true,
      message: '模板创建成功',
      data: { id: result },
      timestamp: new Date().toISOString(),
    };

    res.status(201).json(response);
  })
);

// 更新模板
router.put('/:id',
  authenticateToken,
  requireAdmin,
  [
    body('template_name').optional().notEmpty().withMessage('模板名称不能为空')
      .isLength({ max: 200 }).withMessage('模板名称长度不能超过200个字符'),
    body('template_code').optional().notEmpty().withMessage('模板编号不能为空')
      .isLength({ max: 50 }).withMessage('模板编号长度不能超过50个字符'),
    body('site_type').optional().notEmpty().withMessage('站址类型不能为空'),
    body('description').optional().isString().withMessage('描述必须是字符串'),
    body('version').optional().isString().withMessage('版本号必须是字符串'),
    body('status').optional().isIn(['active', 'inactive', 'draft']).withMessage('状态值无效'),
    body('check_items').optional().isArray().withMessage('检查项目必须是数组'),
  ],
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    // 验证输入
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError('输入数据验证失败');
    }

    const { id } = req.params;
    const {
      template_name,
      template_code,
      site_type,
      description,
      version,
      status,
      check_items
    } = req.body;

    // 检查模板是否存在
    const existingTemplate = await db('inspection_templates')
      .where('id', id)
      .first();

    if (!existingTemplate) {
      throw new NotFoundError('模板不存在');
    }

    // 如果更新模板编号，检查是否重复
    if (template_code && template_code !== existingTemplate.template_code) {
      const duplicateTemplate = await db('inspection_templates')
        .where('template_code', template_code)
        .where('id', '!=', id)
        .first();

      if (duplicateTemplate) {
        throw new ValidationError('模板编号已存在');
      }
    }

    // 开始事务
    await db.transaction(async (trx) => {
      // 更新模板基本信息
      const updateData: any = {
        updated_at: new Date()
      };

      if (template_name !== undefined) updateData.template_name = template_name;
      if (template_code !== undefined) updateData.template_code = template_code;
      if (site_type !== undefined) updateData.site_type = site_type;
      if (description !== undefined) updateData.description = description;
      if (version !== undefined) updateData.version = version;
      if (status !== undefined) updateData.status = status;

      await trx('inspection_templates')
        .where('id', id)
        .update(updateData);

      // 如果提供了检查项目，更新检查项目
      if (check_items !== undefined) {
        // 删除原有检查项目
        await trx('inspection_items')
          .where('template_id', id)
          .del();

        // 插入新的检查项目
        if (check_items.length > 0) {
          const itemsToInsert = check_items.map((item: any, index: number) => ({
            template_id: id,
            step_number: index + 1,
            item_name: item.item_name,
            item_description: item.item_description,
            check_standard: item.check_standard,
            operation_guide: item.operation_guide,
            validation_rules: item.validation_rules ? JSON.stringify(item.validation_rules) : null,
            require_photo: item.require_photo || false,
            require_video: item.require_video || false,
            auto_capture: item.auto_capture || false,
            voice_prompts: item.voice_prompts ? JSON.stringify(item.voice_prompts) : null,
            exception_keywords: item.exception_keywords ? JSON.stringify(item.exception_keywords) : null,
            data_type: item.data_type || 'text',
            options: item.options ? JSON.stringify(item.options) : null,
            is_required: item.is_required !== false,
            created_at: new Date(),
            updated_at: new Date()
          }));

          await trx('inspection_items').insert(itemsToInsert);
        }
      }
    });

    logger.info('模板更新成功:', {
      templateId: id,
      updated_by: req.user?.name,
    });

    const response: ApiResponse = {
      success: true,
      message: '模板更新成功',
      timestamp: new Date().toISOString(),
    };

    res.json(response);
  })
);

// 删除模板
router.delete('/:id',
  authenticateToken,
  requireAdmin,
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { id } = req.params;

    // 检查模板是否存在
    const existingTemplate = await db('inspection_templates')
      .where('id', id)
      .first();

    if (!existingTemplate) {
      throw new NotFoundError('模板不存在');
    }

    // 检查是否有关联的任务
    const relatedTasks = await db('inspection_tasks')
      .where('template_id', id)
      .first();

    if (relatedTasks) {
      throw new ValidationError('该模板已被使用，无法删除');
    }

    // 删除模板（会级联删除检查项目）
    await db('inspection_templates')
      .where('id', id)
      .del();

    logger.info('模板删除成功:', {
      templateId: id,
      deleted_by: req.user?.name,
    });

    const response: ApiResponse = {
      success: true,
      message: '模板删除成功',
      timestamp: new Date().toISOString(),
    };

    res.json(response);
  })
);

// 复制模板
router.post('/:id/copy',
  authenticateToken,
  requireAdmin,
  [
    body('template_name').notEmpty().withMessage('新模板名称不能为空'),
    body('template_code').notEmpty().withMessage('新模板编号不能为空'),
  ],
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    // 验证输入
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError('输入数据验证失败');
    }

    const { id } = req.params;
    const { template_name, template_code } = req.body;

    // 检查原模板是否存在
    const originalTemplate = await db('inspection_templates')
      .where('id', id)
      .first();

    if (!originalTemplate) {
      throw new NotFoundError('原模板不存在');
    }

    // 检查新模板编号是否已存在
    const existingTemplate = await db('inspection_templates')
      .where('template_code', template_code)
      .first();

    if (existingTemplate) {
      throw new ValidationError('模板编号已存在');
    }

    // 获取原模板的检查项目
    const originalItems = await db('inspection_items')
      .where('template_id', id)
      .orderBy('step_number');

    // 开始事务
    const result = await db.transaction(async (trx) => {
      // 创建新模板
      const [newTemplateId] = await trx('inspection_templates').insert({
        template_name,
        template_code,
        site_type: originalTemplate.site_type,
        description: originalTemplate.description,
        version: '1.0',
        status: 'draft',
        created_by: req.user?.id,
        created_at: new Date(),
        updated_at: new Date()
      });

      // 复制检查项目
      if (originalItems.length > 0) {
        const itemsToInsert = originalItems.map((item: any) => ({
          template_id: newTemplateId,
          step_number: item.step_number,
          item_name: item.item_name,
          item_description: item.item_description,
          check_standard: item.check_standard,
          operation_guide: item.operation_guide,
          validation_rules: item.validation_rules,
          require_photo: item.require_photo,
          require_video: item.require_video,
          auto_capture: item.auto_capture,
          voice_prompts: item.voice_prompts,
          exception_keywords: item.exception_keywords,
          data_type: item.data_type,
          options: item.options,
          is_required: item.is_required,
          created_at: new Date(),
          updated_at: new Date()
        }));

        await trx('inspection_items').insert(itemsToInsert);
      }

      return newTemplateId;
    });

    logger.info('模板复制成功:', {
      originalTemplateId: id,
      newTemplateId: result,
      new_template_code: template_code,
      created_by: req.user?.name,
    });

    const response: ApiResponse = {
      success: true,
      message: '模板复制成功',
      data: { id: result },
      timestamp: new Date().toISOString(),
    };

    res.status(201).json(response);
  })
);

export default router;
