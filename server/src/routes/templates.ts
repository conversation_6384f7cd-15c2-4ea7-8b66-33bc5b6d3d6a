import { Router, Response } from 'express';
import { AuthenticatedRequest, ApiResponse } from '../types';
import { authenticateToken, requireAdmin } from '../middleware/auth';
import { asyncHandler } from '../middleware/errorHandler';

const router = Router();

// 所有模板路由都需要认证
router.use(authenticateToken);

// 获取模板列表
router.get('/', authenticateToken, asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  // TODO: 实现模板列表获取逻辑
  const response: ApiResponse = {
    success: true,
    message: '模板管理功能开发中',
    data: [],
    timestamp: new Date().toISOString(),
  };
  res.json(response);
}));

export default router;
