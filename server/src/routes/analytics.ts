import { Router, Response } from 'express';
import { query, validationResult } from 'express-validator';
import { db } from '../utils/database';
import { logger } from '../utils/logger';
import { authenticateToken } from '../middleware/simple-auth';
import { 
  asyncHandler, 
  ValidationError 
} from '../middleware/errorHandler';
import { AuthenticatedRequest, ApiResponse } from '../types';

const router = Router();

// 获取概览统计数据
router.get('/overview', 
  authenticateToken,
  [
    query('start_date').optional().isISO8601().withMessage('开始日期格式无效'),
    query('end_date').optional().isISO8601().withMessage('结束日期格式无效'),
    query('site_type').optional().isString().withMessage('站址类型必须是字符串'),
  ],
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    // 验证输入
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError('输入数据验证失败');
    }

    const { start_date, end_date, site_type } = req.query;

    // 构建日期范围条件
    let dateCondition = '';
    const params: any[] = [];
    
    if (start_date && end_date) {
      dateCondition = 'AND t.created_at BETWEEN ? AND ?';
      params.push(start_date, end_date);
    }

    // 站址类型条件
    let siteTypeCondition = '';
    if (site_type) {
      siteTypeCondition = 'AND s.site_type = ?';
      params.push(site_type);
    }

    // 任务统计
    const taskStats = await db.raw(`
      SELECT 
        COUNT(*) as total_tasks,
        COUNT(CASE WHEN t.status = 'completed' THEN 1 END) as completed_tasks,
        COUNT(CASE WHEN t.status = 'in_progress' THEN 1 END) as in_progress_tasks,
        COUNT(CASE WHEN t.status = 'pending' THEN 1 END) as pending_tasks,
        COUNT(CASE WHEN t.status = 'overdue' THEN 1 END) as overdue_tasks,
        COUNT(CASE WHEN t.status = 'cancelled' THEN 1 END) as cancelled_tasks,
        AVG(CASE WHEN t.actual_duration IS NOT NULL THEN t.actual_duration END) as avg_duration,
        AVG(CASE WHEN t.status = 'completed' THEN t.progress END) as avg_completion_rate
      FROM inspection_tasks t
      LEFT JOIN sites s ON t.site_id = s.id
      WHERE 1=1 ${dateCondition} ${siteTypeCondition}
    `, params);

    // 站址统计
    const siteStats = await db.raw(`
      SELECT 
        COUNT(DISTINCT s.id) as total_sites,
        COUNT(DISTINCT CASE WHEN s.status = 'active' THEN s.id END) as active_sites,
        COUNT(DISTINCT CASE WHEN s.status = 'maintenance' THEN s.id END) as maintenance_sites,
        COUNT(DISTINCT CASE WHEN s.status = 'inactive' THEN s.id END) as inactive_sites
      FROM sites s
      WHERE 1=1 ${site_type ? 'AND s.site_type = ?' : ''}
    `, site_type ? [site_type] : []);

    // 问题统计
    const issueStats = await db.raw(`
      SELECT 
        COUNT(*) as total_issues,
        COUNT(CASE WHEN r.is_exception = 1 THEN 1 END) as exception_count,
        COUNT(CASE WHEN r.status = 'failed' THEN 1 END) as failed_count
      FROM inspection_results r
      LEFT JOIN inspection_tasks t ON r.task_id = t.id
      LEFT JOIN sites s ON t.site_id = s.id
      WHERE 1=1 ${dateCondition} ${siteTypeCondition}
    `, params);

    // 模板使用统计
    const templateStats = await db.raw(`
      SELECT 
        COUNT(DISTINCT tpl.id) as total_templates,
        COUNT(DISTINCT CASE WHEN tpl.status = 'active' THEN tpl.id END) as active_templates
      FROM inspection_templates tpl
      WHERE 1=1 ${site_type ? 'AND tpl.site_type = ?' : ''}
    `, site_type ? [site_type] : []);

    const overview = {
      tasks: taskStats[0][0] || {},
      sites: siteStats[0][0] || {},
      issues: issueStats[0][0] || {},
      templates: templateStats[0][0] || {}
    };

    const response: ApiResponse = {
      success: true,
      message: '获取概览统计成功',
      data: { overview },
      timestamp: new Date().toISOString(),
    };

    res.json(response);
  })
);

// 获取任务趋势数据
router.get('/task-trends', 
  authenticateToken,
  [
    query('days').optional().isInt({ min: 7, max: 365 }).withMessage('天数必须在7-365之间'),
    query('site_type').optional().isString().withMessage('站址类型必须是字符串'),
  ],
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    // 验证输入
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError('输入数据验证失败');
    }

    const { days = 30, site_type } = req.query;

    // 站址类型条件
    let siteTypeCondition = '';
    const params: any[] = [days];
    if (site_type) {
      siteTypeCondition = 'AND s.site_type = ?';
      params.push(site_type);
    }

    const trends = await db.raw(`
      SELECT 
        DATE(t.created_at) as date,
        COUNT(*) as created_count,
        COUNT(CASE WHEN t.status = 'completed' THEN 1 END) as completed_count,
        COUNT(CASE WHEN t.status = 'in_progress' THEN 1 END) as in_progress_count,
        COUNT(CASE WHEN t.status = 'pending' THEN 1 END) as pending_count,
        COUNT(CASE WHEN t.status = 'overdue' THEN 1 END) as overdue_count
      FROM inspection_tasks t
      LEFT JOIN sites s ON t.site_id = s.id
      WHERE t.created_at >= DATE_SUB(CURDATE(), INTERVAL ? DAY) ${siteTypeCondition}
      GROUP BY DATE(t.created_at)
      ORDER BY date ASC
    `, params);

    const response: ApiResponse = {
      success: true,
      message: '获取任务趋势成功',
      data: { trends: trends[0] },
      timestamp: new Date().toISOString(),
    };

    res.json(response);
  })
);

// 获取人员绩效统计
router.get('/user-performance', 
  authenticateToken,
  [
    query('start_date').optional().isISO8601().withMessage('开始日期格式无效'),
    query('end_date').optional().isISO8601().withMessage('结束日期格式无效'),
    query('limit').optional().isInt({ min: 1, max: 50 }).withMessage('限制数量必须在1-50之间'),
  ],
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    // 验证输入
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError('输入数据验证失败');
    }

    const { start_date, end_date, limit = 20 } = req.query;

    // 构建日期范围条件
    let dateCondition = '';
    const params: any[] = [];
    
    if (start_date && end_date) {
      dateCondition = 'AND t.created_at BETWEEN ? AND ?';
      params.push(start_date, end_date);
    }

    params.push(limit);

    const performance = await db.raw(`
      SELECT 
        u.id as user_id,
        u.name as user_name,
        u.department,
        COUNT(*) as total_tasks,
        COUNT(CASE WHEN t.status = 'completed' THEN 1 END) as completed_tasks,
        COUNT(CASE WHEN t.status = 'overdue' THEN 1 END) as overdue_tasks,
        AVG(CASE WHEN t.actual_duration IS NOT NULL THEN t.actual_duration END) as avg_duration,
        AVG(CASE WHEN t.status = 'completed' THEN t.progress END) as avg_completion_rate,
        ROUND(
          COUNT(CASE WHEN t.status = 'completed' THEN 1 END) * 100.0 / COUNT(*), 2
        ) as completion_percentage
      FROM users u
      LEFT JOIN inspection_tasks t ON u.id = t.assigned_to
      WHERE u.status = 'active' AND t.id IS NOT NULL ${dateCondition}
      GROUP BY u.id, u.name, u.department
      HAVING total_tasks > 0
      ORDER BY completion_percentage DESC, completed_tasks DESC
      LIMIT ?
    `, params);

    const response: ApiResponse = {
      success: true,
      message: '获取人员绩效成功',
      data: { performance: performance[0] },
      timestamp: new Date().toISOString(),
    };

    res.json(response);
  })
);

// 获取问题分析数据
router.get('/issue-analysis', 
  authenticateToken,
  [
    query('start_date').optional().isISO8601().withMessage('开始日期格式无效'),
    query('end_date').optional().isISO8601().withMessage('结束日期格式无效'),
    query('site_type').optional().isString().withMessage('站址类型必须是字符串'),
  ],
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    // 验证输入
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError('输入数据验证失败');
    }

    const { start_date, end_date, site_type } = req.query;

    // 构建日期范围条件
    let dateCondition = '';
    const params: any[] = [];
    
    if (start_date && end_date) {
      dateCondition = 'AND r.checked_at BETWEEN ? AND ?';
      params.push(start_date, end_date);
    }

    // 站址类型条件
    let siteTypeCondition = '';
    if (site_type) {
      siteTypeCondition = 'AND s.site_type = ?';
      params.push(site_type);
    }

    // 按检查项目分类的问题统计
    const issuesByCategory = await db.raw(`
      SELECT 
        i.item_name as category,
        COUNT(CASE WHEN r.is_exception = 1 THEN 1 END) as exception_count,
        COUNT(CASE WHEN r.status = 'failed' THEN 1 END) as failed_count,
        COUNT(*) as total_checks,
        ROUND(
          COUNT(CASE WHEN r.is_exception = 1 OR r.status = 'failed' THEN 1 END) * 100.0 / COUNT(*), 2
        ) as issue_rate
      FROM inspection_results r
      LEFT JOIN inspection_items i ON r.item_id = i.id
      LEFT JOIN inspection_tasks t ON r.task_id = t.id
      LEFT JOIN sites s ON t.site_id = s.id
      WHERE 1=1 ${dateCondition} ${siteTypeCondition}
      GROUP BY i.item_name
      HAVING total_checks > 0
      ORDER BY issue_rate DESC, exception_count DESC
    `, params);

    // 最近的异常问题
    const recentIssues = await db.raw(`
      SELECT 
        r.id,
        s.site_name,
        s.site_code,
        i.item_name as issue_type,
        r.exception_reason as description,
        r.checked_at as found_at,
        r.status,
        u.name as checked_by_name,
        CASE 
          WHEN r.status = 'failed' THEN 'critical'
          WHEN r.is_exception = 1 THEN 'high'
          ELSE 'medium'
        END as severity
      FROM inspection_results r
      LEFT JOIN inspection_items i ON r.item_id = i.id
      LEFT JOIN inspection_tasks t ON r.task_id = t.id
      LEFT JOIN sites s ON t.site_id = s.id
      LEFT JOIN users u ON r.checked_by = u.id
      WHERE (r.is_exception = 1 OR r.status = 'failed') ${dateCondition} ${siteTypeCondition}
      ORDER BY r.checked_at DESC
      LIMIT 20
    `, params);

    const response: ApiResponse = {
      success: true,
      message: '获取问题分析成功',
      data: { 
        issuesByCategory: issuesByCategory[0],
        recentIssues: recentIssues[0]
      },
      timestamp: new Date().toISOString(),
    };

    res.json(response);
  })
);

// 获取站址状态分布
router.get('/site-distribution', 
  authenticateToken,
  [
    query('site_type').optional().isString().withMessage('站址类型必须是字符串'),
  ],
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    // 验证输入
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError('输入数据验证失败');
    }

    const { site_type } = req.query;

    // 站址类型条件
    let siteTypeCondition = '';
    const params: any[] = [];
    if (site_type) {
      siteTypeCondition = 'WHERE site_type = ?';
      params.push(site_type);
    }

    // 按状态分布
    const statusDistribution = await db.raw(`
      SELECT 
        status,
        COUNT(*) as count,
        ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM sites ${siteTypeCondition}), 2) as percentage
      FROM sites
      ${siteTypeCondition}
      GROUP BY status
      ORDER BY count DESC
    `, params);

    // 按类型分布
    const typeDistribution = await db.raw(`
      SELECT 
        site_type,
        COUNT(*) as count,
        ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM sites), 2) as percentage
      FROM sites
      GROUP BY site_type
      ORDER BY count DESC
    `);

    // 按地区分布
    const regionDistribution = await db.raw(`
      SELECT 
        SUBSTRING_INDEX(address, ' ', 1) as region,
        COUNT(*) as count
      FROM sites
      ${siteTypeCondition}
      GROUP BY region
      ORDER BY count DESC
      LIMIT 10
    `, params);

    const response: ApiResponse = {
      success: true,
      message: '获取站址分布成功',
      data: { 
        statusDistribution: statusDistribution[0],
        typeDistribution: typeDistribution[0],
        regionDistribution: regionDistribution[0]
      },
      timestamp: new Date().toISOString(),
    };

    res.json(response);
  })
);

export default router;
