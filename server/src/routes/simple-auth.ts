import { Router, Response } from 'express';
import { body, validationResult } from 'express-validator';
import { db } from '../utils/database';
import { logger } from '../utils/logger';
import { 
  generateTokens, 
  verifyPassword, 
  hashPassword,
  authenticateToken 
} from '../middleware/simple-auth';
import {
  asyncHandler,
  ValidationError,
  NotFoundError
} from '../middleware/errorHandler';

// 简单的未授权错误类
class UnauthorizedError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'UnauthorizedError';
  }
}
import { AuthenticatedRequest, ApiResponse, User } from '../types';

const router = Router();

// 登录验证规则
const loginValidation = [
  body('employee_id')
    .notEmpty()
    .withMessage('工号不能为空')
    .isLength({ min: 1, max: 50 })
    .withMessage('工号长度必须在1-50个字符之间'),
  body('password')
    .notEmpty()
    .withMessage('密码不能为空')
    .isLength({ min: 6 })
    .withMessage('密码长度至少6个字符'),
];

// 用户登录
router.post('/login', loginValidation, asyncHandler(async (req, res: Response) => {
  // 验证输入
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new ValidationError('输入数据验证失败');
  }

  const { employee_id, password } = req.body;

  // 查找用户
  const user = await db('users')
    .where({ employee_id })
    .first();

  if (!user) {
    throw new NotFoundError('用户不存在');
  }

  if (user.status !== 'active') {
    throw new UnauthorizedError('用户账户已被禁用');
  }

  // 验证密码
  const isPasswordValid = await verifyPassword(password, user.password);
  if (!isPasswordValid) {
    throw new UnauthorizedError('密码错误');
  }

  // 生成令牌
  const tokens = generateTokens(user);

  // 更新最后登录时间
  await db('users')
    .where({ id: user.id })
    .update({ 
      last_login_at: new Date(),
      updated_at: new Date()
    });

  logger.info('用户登录成功:', {
    userId: user.id,
    employeeId: user.employee_id,
    name: user.name,
    ip: req.ip,
  });

  // 返回用户信息和令牌（不包含密码）
  const { password: _, ...userWithoutPassword } = user;

  const response: ApiResponse = {
    success: true,
    message: '登录成功',
    data: {
      user: userWithoutPassword,
      ...tokens,
    },
    timestamp: new Date().toISOString(),
  };

  res.json(response);
}));

// 获取当前用户信息
router.get('/me', authenticateToken, asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  if (!req.user) {
    throw new UnauthorizedError('用户未认证');
  }

  // 返回用户信息（不包含密码）
  const { password: _, ...userWithoutPassword } = req.user as any;

  const response: ApiResponse = {
    success: true,
    message: '获取用户信息成功',
    data: { user: userWithoutPassword },
    timestamp: new Date().toISOString(),
  };

  res.json(response);
}));

// 用户登出
router.post('/logout', authenticateToken, asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  logger.info('用户登出:', {
    userId: req.user?.id,
    employeeId: req.user?.employee_id,
    name: req.user?.name,
  });

  const response: ApiResponse = {
    success: true,
    message: '登出成功',
    timestamp: new Date().toISOString(),
  };

  res.json(response);
}));

export default router;
