import { Router, Response } from 'express';
import { AuthenticatedRequest, ApiResponse } from '../types';
import { authenticateToken, requireAdmin } from '../middleware/auth';
import { asyncHandler } from '../middleware/errorHandler';

const router = Router();

// 所有设备路由都需要认证
router.use(authenticateToken);

// 获取设备列表
router.get('/', requireAdmin, asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  // TODO: 实现设备列表获取逻辑
  const response: ApiResponse = {
    success: true,
    message: '设备管理功能开发中',
    data: [],
    timestamp: new Date().toISOString(),
  };
  res.json(response);
}));

export default router;
