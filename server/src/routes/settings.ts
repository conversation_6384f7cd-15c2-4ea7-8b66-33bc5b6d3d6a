import { Router, Response } from 'express';
import { query, body, validationResult } from 'express-validator';
import { db } from '../utils/database';
import { logger } from '../utils/logger';
import { authenticateToken, requireAdmin } from '../middleware/simple-auth';
import { 
  asyncHandler, 
  ValidationError, 
  NotFoundError 
} from '../middleware/errorHandler';
import { AuthenticatedRequest, ApiResponse, UserRole } from '../types';

const router = Router();

// 获取用户列表
router.get('/users', 
  authenticateToken,
  requireAdmin,
  [
    query('page').optional().isInt({ min: 1 }).withMessage('页码必须是正整数'),
    query('pageSize').optional().isInt({ min: 1, max: 100 }).withMessage('每页大小必须在1-100之间'),
    query('search').optional().isString().withMessage('搜索关键词必须是字符串'),
    query('role').optional().isString().withMessage('角色必须是字符串'),
    query('status').optional().isIn(['active', 'inactive']).withMessage('状态值无效'),
    query('department').optional().isString().withMessage('部门必须是字符串'),
  ],
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    // 验证输入
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError('输入数据验证失败');
    }

    const {
      page = 1,
      pageSize = 20,
      search,
      role,
      status,
      department
    } = req.query;

    // 构建查询
    let query = db('users')
      .select(
        'id',
        'employee_id',
        'name',
        'email',
        'phone',
        'department',
        'role',
        'status',
        'last_login_at',
        'created_at',
        'updated_at'
      );

    // 搜索条件
    if (search) {
      query = query.where(function() {
        this.where('name', 'like', `%${search}%`)
          .orWhere('employee_id', 'like', `%${search}%`)
          .orWhere('email', 'like', `%${search}%`);
      });
    }

    // 筛选条件
    if (role) {
      query = query.where('role', role);
    }
    if (status) {
      query = query.where('status', status);
    }
    if (department) {
      query = query.where('department', department);
    }

    // 获取总数
    const totalQuery = query.clone();
    const [{ count }] = await totalQuery.count('id as count');
    const total = parseInt(count as string);

    // 分页
    const offset = (Number(page) - 1) * Number(pageSize);
    query = query.orderBy('created_at', 'desc')
      .limit(Number(pageSize))
      .offset(offset);

    const users = await query;

    const response: ApiResponse = {
      success: true,
      message: '获取用户列表成功',
      data: {
        users,
        pagination: {
          page: Number(page),
          pageSize: Number(pageSize),
          total,
          totalPages: Math.ceil(total / Number(pageSize)),
        },
      },
      timestamp: new Date().toISOString(),
    };

    res.json(response);
  })
);

// 创建用户
router.post('/users', 
  authenticateToken,
  requireAdmin,
  [
    body('employee_id').notEmpty().withMessage('工号不能为空')
      .isLength({ max: 50 }).withMessage('工号长度不能超过50个字符'),
    body('name').notEmpty().withMessage('姓名不能为空')
      .isLength({ max: 100 }).withMessage('姓名长度不能超过100个字符'),
    body('email').isEmail().withMessage('邮箱格式无效'),
    body('phone').optional().isMobilePhone('zh-CN').withMessage('手机号格式无效'),
    body('department').notEmpty().withMessage('部门不能为空'),
    body('role').isIn(['super_admin', 'site_admin', 'inspector', 'viewer']).withMessage('角色值无效'),
    body('password').isLength({ min: 6 }).withMessage('密码长度至少6位'),
  ],
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    // 验证输入
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError('输入数据验证失败');
    }

    const {
      employee_id,
      name,
      email,
      phone,
      department,
      role,
      password
    } = req.body;

    // 检查工号是否已存在
    const existingUser = await db('users')
      .where('employee_id', employee_id)
      .first();

    if (existingUser) {
      throw new ValidationError('工号已存在');
    }

    // 检查邮箱是否已存在
    if (email) {
      const existingEmail = await db('users')
        .where('email', email)
        .first();

      if (existingEmail) {
        throw new ValidationError('邮箱已存在');
      }
    }

    // 创建用户
    const bcrypt = require('bcrypt');
    const hashedPassword = await bcrypt.hash(password, 10);

    const [userId] = await db('users').insert({
      employee_id,
      name,
      email,
      phone,
      department,
      role,
      password_hash: hashedPassword,
      status: 'active',
      created_at: new Date(),
      updated_at: new Date()
    });

    logger.info('用户创建成功:', {
      userId,
      employee_id,
      created_by: req.user?.name,
    });

    const response: ApiResponse = {
      success: true,
      message: '用户创建成功',
      data: { id: userId },
      timestamp: new Date().toISOString(),
    };

    res.status(201).json(response);
  })
);

// 更新用户
router.put('/users/:id', 
  authenticateToken,
  requireAdmin,
  [
    body('name').optional().notEmpty().withMessage('姓名不能为空')
      .isLength({ max: 100 }).withMessage('姓名长度不能超过100个字符'),
    body('email').optional().isEmail().withMessage('邮箱格式无效'),
    body('phone').optional().isMobilePhone('zh-CN').withMessage('手机号格式无效'),
    body('department').optional().notEmpty().withMessage('部门不能为空'),
    body('role').optional().isIn(['super_admin', 'site_admin', 'inspector', 'viewer']).withMessage('角色值无效'),
    body('status').optional().isIn(['active', 'inactive']).withMessage('状态值无效'),
  ],
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    // 验证输入
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError('输入数据验证失败');
    }

    const { id } = req.params;
    const {
      name,
      email,
      phone,
      department,
      role,
      status
    } = req.body;

    // 检查用户是否存在
    const existingUser = await db('users')
      .where('id', id)
      .first();

    if (!existingUser) {
      throw new NotFoundError('用户不存在');
    }

    // 防止修改自己的角色和状态
    if (req.user?.id === parseInt(id)) {
      if (role && role !== existingUser.role) {
        throw new ValidationError('不能修改自己的角色');
      }
      if (status && status !== existingUser.status) {
        throw new ValidationError('不能修改自己的状态');
      }
    }

    // 检查邮箱是否重复
    if (email && email !== existingUser.email) {
      const duplicateEmail = await db('users')
        .where('email', email)
        .where('id', '!=', id)
        .first();

      if (duplicateEmail) {
        throw new ValidationError('邮箱已存在');
      }
    }

    // 更新用户信息
    const updateData: any = {
      updated_at: new Date()
    };

    if (name !== undefined) updateData.name = name;
    if (email !== undefined) updateData.email = email;
    if (phone !== undefined) updateData.phone = phone;
    if (department !== undefined) updateData.department = department;
    if (role !== undefined) updateData.role = role;
    if (status !== undefined) updateData.status = status;

    await db('users')
      .where('id', id)
      .update(updateData);

    logger.info('用户更新成功:', {
      userId: id,
      updated_by: req.user?.name,
    });

    const response: ApiResponse = {
      success: true,
      message: '用户更新成功',
      timestamp: new Date().toISOString(),
    };

    res.json(response);
  })
);

// 删除用户
router.delete('/users/:id', 
  authenticateToken,
  requireAdmin,
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { id } = req.params;

    // 检查用户是否存在
    const existingUser = await db('users')
      .where('id', id)
      .first();

    if (!existingUser) {
      throw new NotFoundError('用户不存在');
    }

    // 防止删除自己
    if (req.user?.id === parseInt(id)) {
      throw new ValidationError('不能删除自己');
    }

    // 检查是否有关联的任务
    const relatedTasks = await db('inspection_tasks')
      .where('assigned_to', id)
      .orWhere('created_by', id)
      .first();

    if (relatedTasks) {
      throw new ValidationError('该用户有关联的任务，无法删除');
    }

    // 删除用户
    await db('users')
      .where('id', id)
      .del();

    logger.info('用户删除成功:', {
      userId: id,
      deleted_by: req.user?.name,
    });

    const response: ApiResponse = {
      success: true,
      message: '用户删除成功',
      timestamp: new Date().toISOString(),
    };

    res.json(response);
  })
);

// 获取系统配置
router.get('/configs',
  authenticateToken,
  [
    query('category').optional().isString().withMessage('分类必须是字符串'),
    query('is_public').optional().isBoolean().withMessage('是否公开必须是布尔值'),
  ],
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    // 验证输入
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError('输入数据验证失败');
    }

    const { category, is_public } = req.query;

    // 构建查询
    let query = db('system_configs')
      .select(
        'id',
        'config_key',
        'config_name',
        'config_value',
        'default_value',
        'description',
        'data_type',
        'category',
        'is_public',
        'is_editable',
        'validation_rules',
        'sort_order'
      );

    // 权限控制：非管理员只能看到公开配置
    if (req.user?.role !== UserRole.SUPER_ADMIN && req.user?.role !== UserRole.SITE_ADMIN) {
      query = query.where('is_public', true);
    } else if (is_public !== undefined) {
      query = query.where('is_public', is_public);
    }

    // 分类筛选
    if (category) {
      query = query.where('category', category);
    }

    const configs = await query.orderBy('category').orderBy('sort_order');

    const response: ApiResponse = {
      success: true,
      message: '获取系统配置成功',
      data: { configs },
      timestamp: new Date().toISOString(),
    };

    res.json(response);
  })
);

// 更新系统配置
router.put('/configs/:key',
  authenticateToken,
  requireAdmin,
  [
    body('config_value').notEmpty().withMessage('配置值不能为空'),
  ],
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    // 验证输入
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError('输入数据验证失败');
    }

    const { key } = req.params;
    const { config_value } = req.body;

    // 检查配置是否存在
    const existingConfig = await db('system_configs')
      .where('config_key', key)
      .first();

    if (!existingConfig) {
      throw new NotFoundError('配置项不存在');
    }

    // 检查是否可编辑
    if (!existingConfig.is_editable) {
      throw new ValidationError('该配置项不可编辑');
    }

    // 验证配置值
    if (existingConfig.validation_rules) {
      const rules = JSON.parse(existingConfig.validation_rules);

      // 简单的验证逻辑
      if (rules.required && !config_value) {
        throw new ValidationError('配置值不能为空');
      }

      if (existingConfig.data_type === 'number') {
        const numValue = Number(config_value);
        if (isNaN(numValue)) {
          throw new ValidationError('配置值必须是数字');
        }
        if (rules.min !== undefined && numValue < rules.min) {
          throw new ValidationError(`配置值不能小于 ${rules.min}`);
        }
        if (rules.max !== undefined && numValue > rules.max) {
          throw new ValidationError(`配置值不能大于 ${rules.max}`);
        }
      }

      if (existingConfig.data_type === 'boolean') {
        if (!['true', 'false'].includes(config_value.toLowerCase())) {
          throw new ValidationError('配置值必须是 true 或 false');
        }
      }
    }

    // 更新配置
    await db('system_configs')
      .where('config_key', key)
      .update({
        config_value,
        updated_at: new Date()
      });

    logger.info('系统配置更新成功:', {
      config_key: key,
      old_value: existingConfig.config_value,
      new_value: config_value,
      updated_by: req.user?.name,
    });

    const response: ApiResponse = {
      success: true,
      message: '配置更新成功',
      timestamp: new Date().toISOString(),
    };

    res.json(response);
  })
);

// 重置配置到默认值
router.post('/configs/:key/reset',
  authenticateToken,
  requireAdmin,
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { key } = req.params;

    // 检查配置是否存在
    const existingConfig = await db('system_configs')
      .where('config_key', key)
      .first();

    if (!existingConfig) {
      throw new NotFoundError('配置项不存在');
    }

    // 检查是否可编辑
    if (!existingConfig.is_editable) {
      throw new ValidationError('该配置项不可编辑');
    }

    // 重置到默认值
    await db('system_configs')
      .where('config_key', key)
      .update({
        config_value: existingConfig.default_value,
        updated_at: new Date()
      });

    logger.info('系统配置重置成功:', {
      config_key: key,
      reset_to: existingConfig.default_value,
      reset_by: req.user?.name,
    });

    const response: ApiResponse = {
      success: true,
      message: '配置重置成功',
      timestamp: new Date().toISOString(),
    };

    res.json(response);
  })
);

// 获取配置分类列表
router.get('/config-categories',
  authenticateToken,
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    let query = db('system_configs')
      .distinct('category')
      .select('category');

    // 权限控制：非管理员只能看到公开配置的分类
    if (req.user?.role !== UserRole.SUPER_ADMIN && req.user?.role !== UserRole.SITE_ADMIN) {
      query = query.where('is_public', true);
    }

    const categories = await query.orderBy('category');

    const response: ApiResponse = {
      success: true,
      message: '获取配置分类成功',
      data: { categories: categories.map(c => c.category) },
      timestamp: new Date().toISOString(),
    };

    res.json(response);
  })
);

export default router;
