import { Router, Response } from 'express';
import { AuthenticatedRequest, ApiResponse } from '../types';
import { authenticateToken } from '../middleware/auth';
import { asyncHandler } from '../middleware/errorHandler';

const router = Router();

// 所有上传路由都需要认证
router.use(authenticateToken);

// 文件上传
router.post('/', authenticateToken, asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  // TODO: 实现文件上传逻辑
  const response: ApiResponse = {
    success: true,
    message: '文件上传功能开发中',
    data: null,
    timestamp: new Date().toISOString(),
  };
  res.json(response);
}));

export default router;
