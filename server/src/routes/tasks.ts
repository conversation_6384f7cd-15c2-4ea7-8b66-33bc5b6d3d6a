import { Router, Response } from 'express';
import { query, body, validationResult } from 'express-validator';
import { db } from '../utils/database';
import { logger } from '../utils/logger';
import { authenticateToken, requireAdmin } from '../middleware/simple-auth';
import {
  asyncHandler,
  ValidationError,
  NotFoundError
} from '../middleware/errorHandler';
import { AuthenticatedRequest, ApiResponse, UserRole } from '../types';

const router = Router();

// 生成任务编号
function generateTaskCode(): string {
  const now = new Date();
  const dateStr = now.toISOString().slice(0, 10).replace(/-/g, '');
  const timeStr = now.getTime().toString().slice(-6);
  return `TASK-${dateStr}-${timeStr}`;
}

// 记录任务历史
async function recordTaskHistory(
  taskId: number,
  action: string,
  description: string,
  userId: number,
  oldData?: any,
  newData?: any,
  req?: any
) {
  await db('task_history').insert({
    task_id: taskId,
    action,
    description,
    old_data: oldData ? JSON.stringify(oldData) : null,
    new_data: newData ? JSON.stringify(newData) : null,
    ip_address: req?.ip || null,
    user_agent: req?.get('User-Agent') || null,
    user_id: userId,
    created_at: new Date(),
    updated_at: new Date()
  });
}

// 获取任务列表
router.get('/',
  authenticateToken,
  [
    query('page').optional().isInt({ min: 1 }).withMessage('页码必须是正整数'),
    query('pageSize').optional().isInt({ min: 1, max: 100 }).withMessage('每页大小必须在1-100之间'),
    query('search').optional().isString().withMessage('搜索关键词必须是字符串'),
    query('status').optional().isIn(['pending', 'in_progress', 'completed', 'cancelled', 'overdue']).withMessage('状态值无效'),
    query('priority').optional().isIn(['low', 'medium', 'high', 'urgent']).withMessage('优先级值无效'),
    query('assigned_to').optional().isInt().withMessage('分配人ID必须是整数'),
    query('template_id').optional().isInt().withMessage('模板ID必须是整数'),
    query('site_id').optional().isInt().withMessage('站址ID必须是整数'),
    query('sortBy').optional().isString().withMessage('排序字段必须是字符串'),
    query('sortOrder').optional().isIn(['asc', 'desc']).withMessage('排序方向必须是asc或desc'),
  ],
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    // 验证输入
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError('输入数据验证失败');
    }

    const {
      page = 1,
      pageSize = 20,
      sortBy = 'created_at',
      sortOrder = 'desc',
      search,
      status,
      priority,
      assigned_to,
      template_id,
      site_id
    } = req.query;

    // 构建查询
    let query = db('inspection_tasks as t')
      .leftJoin('inspection_templates as tpl', 't.template_id', 'tpl.id')
      .leftJoin('sites as s', 't.site_id', 's.id')
      .leftJoin('users as u1', 't.assigned_to', 'u1.id')
      .leftJoin('users as u2', 't.created_by', 'u2.id')
      .select(
        't.*',
        'tpl.template_name',
        'tpl.template_code',
        's.site_name',
        's.site_code',
        'u1.name as assigned_user_name',
        'u2.name as created_by_name'
      );

    // 权限控制：普通用户只能看到分配给自己的任务
    if (req.user?.role === UserRole.INSPECTOR || req.user?.role === UserRole.VIEWER) {
      query = query.where('t.assigned_to', req.user.id);
    }

    // 搜索条件
    if (search) {
      query = query.where(function() {
        this.where('t.task_name', 'like', `%${search}%`)
          .orWhere('t.task_code', 'like', `%${search}%`)
          .orWhere('t.description', 'like', `%${search}%`)
          .orWhere('s.site_name', 'like', `%${search}%`)
          .orWhere('s.site_code', 'like', `%${search}%`);
      });
    }

    // 筛选条件
    if (status) {
      query = query.where('t.status', status);
    }
    if (priority) {
      query = query.where('t.priority', priority);
    }
    if (assigned_to) {
      query = query.where('t.assigned_to', assigned_to);
    }
    if (template_id) {
      query = query.where('t.template_id', template_id);
    }
    if (site_id) {
      query = query.where('t.site_id', site_id);
    }

    // 获取总数
    const totalQuery = query.clone();
    const [{ count }] = await totalQuery.count('t.id as count');
    const total = parseInt(count as string);

    // 排序和分页
    const validSortFields = [
      'task_name', 'task_code', 'status', 'priority', 'scheduled_date', 'due_date',
      'created_at', 'updated_at', 'progress'
    ];

    const sortField = validSortFields.includes(sortBy as string) ? `t.${sortBy}` : 't.created_at';
    query = query.orderBy(sortField, sortOrder as 'asc' | 'desc');

    const offset = (Number(page) - 1) * Number(pageSize);
    query = query.limit(Number(pageSize)).offset(offset);

    const tasks = await query;

    // 更新逾期任务状态
    const now = new Date();
    await db('inspection_tasks')
      .where('due_date', '<', now)
      .whereIn('status', ['pending', 'in_progress'])
      .update({ status: 'overdue', updated_at: now });

    const response: ApiResponse = {
      success: true,
      message: '获取任务列表成功',
      data: {
        tasks,
        pagination: {
          page: Number(page),
          pageSize: Number(pageSize),
          total,
          totalPages: Math.ceil(total / Number(pageSize)),
        },
      },
      timestamp: new Date().toISOString(),
    };

    res.json(response);
  })
);

// 获取任务详情
router.get('/:id',
  authenticateToken,
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { id } = req.params;

    const task = await db('inspection_tasks as t')
      .leftJoin('inspection_templates as tpl', 't.template_id', 'tpl.id')
      .leftJoin('sites as s', 't.site_id', 's.id')
      .leftJoin('users as u1', 't.assigned_to', 'u1.id')
      .leftJoin('users as u2', 't.created_by', 'u2.id')
      .select(
        't.*',
        'tpl.template_name',
        'tpl.template_code',
        's.site_name',
        's.site_code',
        's.address as site_address',
        'u1.name as assigned_user_name',
        'u1.phone as assigned_user_phone',
        'u2.name as created_by_name'
      )
      .where('t.id', id)
      .first();

    if (!task) {
      throw new NotFoundError('任务不存在');
    }

    // 权限控制：普通用户只能查看分配给自己的任务
    if ((req.user?.role === UserRole.INSPECTOR || req.user?.role === UserRole.VIEWER) && task.assigned_to !== req.user.id) {
      throw new NotFoundError('任务不存在');
    }

    // 获取检查项目和结果
    const checkItems = await db('inspection_items as i')
      .leftJoin('inspection_results as r', function() {
        this.on('i.id', '=', 'r.item_id')
          .andOn('r.task_id', '=', db.raw('?', [id]));
      })
      .select(
        'i.*',
        'r.id as result_id',
        'r.status as result_status',
        'r.result_value',
        'r.notes as result_notes',
        'r.photos',
        'r.videos',
        'r.is_exception',
        'r.exception_reason',
        'r.checked_at',
        'r.checked_by'
      )
      .where('i.template_id', task.template_id)
      .orderBy('i.step_number');

    // 获取任务历史
    const history = await db('task_history as h')
      .leftJoin('users as u', 'h.user_id', 'u.id')
      .select('h.*', 'u.name as user_name')
      .where('h.task_id', id)
      .orderBy('h.created_at', 'desc');

    const response: ApiResponse = {
      success: true,
      message: '获取任务详情成功',
      data: {
        task: {
          ...task,
          check_items: checkItems,
          history
        }
      },
      timestamp: new Date().toISOString(),
    };

    res.json(response);
  })
);

export default router;
