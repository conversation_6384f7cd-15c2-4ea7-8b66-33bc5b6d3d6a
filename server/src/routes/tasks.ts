import { Router, Response } from 'express';
import { AuthenticatedRequest, ApiResponse } from '../types';
import { authenticateToken } from '../middleware/auth';
import { asyncHandler } from '../middleware/errorHandler';

const router = Router();

// 所有任务路由都需要认证
router.use(authenticateToken);

// 获取任务列表
router.get('/', authenticateToken, asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  // TODO: 实现任务列表获取逻辑
  const response: ApiResponse = {
    success: true,
    message: '任务管理功能开发中',
    data: [],
    timestamp: new Date().toISOString(),
  };
  res.json(response);
}));

export default router;
