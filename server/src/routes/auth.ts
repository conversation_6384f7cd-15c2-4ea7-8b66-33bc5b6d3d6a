import { Router, Request, Response } from 'express';
import bcrypt from 'bcryptjs';
import { body, validationResult } from 'express-validator';
import { db } from '../utils/database';
import { logger } from '../utils/logger';
import { 
  asyncHandler, 
  ValidationError, 
  AuthenticationError,
  NotFoundError 
} from '../middleware/errorHandler';
import { 
  generateTokens, 
  verifyRefreshToken,
  authenticateToken 
} from '../middleware/auth';
import { AuthenticatedRequest, ApiResponse, User } from '../types';
import { config } from '../config';

const router = Router();

// 登录验证规则
const loginValidation = [
  body('employeeId')
    .notEmpty()
    .withMessage('工号不能为空')
    .isLength({ min: 1, max: 50 })
    .withMessage('工号长度必须在1-50个字符之间'),
  body('password')
    .notEmpty()
    .withMessage('密码不能为空')
    .isLength({ min: 6 })
    .withMessage('密码长度至少6个字符'),
];

// 用户登录
router.post('/login', loginValidation, asyncHandler(async (req: Request, res: Response) => {
  // 验证输入
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new ValidationError('输入数据验证失败');
  }

  const { employeeId, password } = req.body;

  // 查找用户
  const user = await db('users')
    .where({ employee_id: employeeId })
    .first();

  if (!user) {
    logger.warn('登录失败 - 用户不存在:', { employeeId, ip: req.ip });
    throw new AuthenticationError('工号或密码错误');
  }

  // 检查用户状态
  if (user.status !== 'active') {
    logger.warn('登录失败 - 用户已被禁用:', { employeeId, status: user.status, ip: req.ip });
    throw new AuthenticationError('账户已被禁用，请联系管理员');
  }

  // 验证密码
  const isPasswordValid = await bcrypt.compare(password, user.password_hash);
  if (!isPasswordValid) {
    logger.warn('登录失败 - 密码错误:', { employeeId, ip: req.ip });
    throw new AuthenticationError('工号或密码错误');
  }

  // 生成令牌
  const tokens = generateTokens(user);

  // 更新最后登录时间
  await db('users')
    .where({ id: user.id })
    .update({ last_login_at: new Date() });

  // 记录登录日志
  logger.info('用户登录成功:', {
    userId: user.id,
    employeeId: user.employee_id,
    name: user.name,
    role: user.role,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
  });

  // 返回用户信息和令牌（不包含密码）
  const { password_hash, ...userInfo } = user;
  
  const response: ApiResponse = {
    success: true,
    message: '登录成功',
    data: {
      user: userInfo,
      tokens,
    },
    timestamp: new Date().toISOString(),
  };

  res.json(response);
}));

// 刷新令牌
router.post('/refresh', asyncHandler(async (req: Request, res: Response) => {
  const { refreshToken } = req.body;

  if (!refreshToken) {
    throw new ValidationError('刷新令牌不能为空');
  }

  // 验证刷新令牌
  const decoded = verifyRefreshToken(refreshToken);

  // 查找用户
  const user = await db('users')
    .where({ id: decoded.userId, status: 'active' })
    .first();

  if (!user) {
    throw new AuthenticationError('用户不存在或已被禁用');
  }

  // 生成新的令牌
  const tokens = generateTokens(user);

  logger.info('令牌刷新成功:', {
    userId: user.id,
    employeeId: user.employee_id,
    ip: req.ip,
  });

  const response: ApiResponse = {
    success: true,
    message: '令牌刷新成功',
    data: { tokens },
    timestamp: new Date().toISOString(),
  };

  res.json(response);
}));

// 获取当前用户信息
router.get('/me', authenticateToken, asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  if (!req.user) {
    throw new AuthenticationError('用户未认证');
  }

  // 获取最新的用户信息
  const user = await db('users')
    .select('id', 'employee_id', 'name', 'role', 'department', 'email', 'phone', 'status', 'created_at', 'last_login_at')
    .where({ id: req.user.id })
    .first();

  if (!user) {
    throw new NotFoundError('用户信息未找到');
  }

  const response: ApiResponse = {
    success: true,
    message: '获取用户信息成功',
    data: { user },
    timestamp: new Date().toISOString(),
  };

  res.json(response);
}));

// 修改密码验证规则
const changePasswordValidation = [
  body('currentPassword')
    .notEmpty()
    .withMessage('当前密码不能为空'),
  body('newPassword')
    .isLength({ min: 6 })
    .withMessage('新密码长度至少6个字符')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('新密码必须包含大小写字母和数字'),
  body('confirmPassword')
    .custom((value, { req }) => {
      if (value !== req.body.newPassword) {
        throw new Error('确认密码与新密码不匹配');
      }
      return true;
    }),
];

// 修改密码
router.put('/change-password', 
  authenticateToken, 
  changePasswordValidation, 
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    // 验证输入
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError('输入数据验证失败');
    }

    if (!req.user) {
      throw new AuthenticationError('用户未认证');
    }

    const { currentPassword, newPassword } = req.body;
    const userId = req.user.id;

    // 获取用户当前密码
    const user = await db('users')
      .select('password_hash')
      .where({ id: userId })
      .first();

    if (!user) {
      throw new NotFoundError('用户不存在');
    }

    // 验证当前密码
    const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password_hash);
    if (!isCurrentPasswordValid) {
      throw new AuthenticationError('当前密码错误');
    }

    // 加密新密码
    const newPasswordHash = await bcrypt.hash(newPassword, config.security.bcryptRounds);

    // 更新密码
    await db('users')
      .where({ id: userId })
      .update({ 
        password_hash: newPasswordHash,
        updated_at: new Date()
      });

    logger.info('用户修改密码成功:', {
      userId,
      employeeId: req.user.employee_id,
      ip: req.ip,
    });

    const response: ApiResponse = {
      success: true,
      message: '密码修改成功',
      timestamp: new Date().toISOString(),
    };

    res.json(response);
  })
);

// 登出
router.post('/logout', authenticateToken, asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  // 这里可以实现令牌黑名单机制
  // 目前只是记录登出日志
  
  if (req.user) {
    logger.info('用户登出:', {
      userId: req.user.id,
      employeeId: req.user.employee_id,
      ip: req.ip,
    });
  }

  const response: ApiResponse = {
    success: true,
    message: '登出成功',
    timestamp: new Date().toISOString(),
  };

  res.json(response);
}));

export default router;
