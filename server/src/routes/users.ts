import { Router, Response } from 'express';
import bcrypt from 'bcryptjs';
import { body, validationResult } from 'express-validator';
import { db, QueryBuilder } from '../utils/database';
import { logger } from '../utils/logger';
import { 
  asyncHandler, 
  ValidationError, 
  NotFoundError,
  ConflictError 
} from '../middleware/errorHandler';
import { 
  authenticateToken, 
  requireAdmin,
  requireSuperAdmin,
  checkResourceAccess 
} from '../middleware/auth';
import { AuthenticatedRequest, ApiResponse, PaginatedResponse, User, UserRole } from '../types';
import { config } from '../config';

const router = Router();

// 所有用户路由都需要认证
router.use(authenticateToken);

// 用户创建验证规则
const createUserValidation = [
  body('employee_id')
    .notEmpty()
    .withMessage('工号不能为空')
    .isLength({ min: 1, max: 50 })
    .withMessage('工号长度必须在1-50个字符之间'),
  body('name')
    .notEmpty()
    .withMessage('姓名不能为空')
    .isLength({ min: 1, max: 100 })
    .withMessage('姓名长度必须在1-100个字符之间'),
  body('role')
    .isIn(Object.values(UserRole))
    .withMessage('无效的用户角色'),
  body('password')
    .isLength({ min: 6 })
    .withMessage('密码长度至少6个字符')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('密码必须包含大小写字母和数字'),
  body('email')
    .optional()
    .isEmail()
    .withMessage('邮箱格式不正确'),
  body('phone')
    .optional()
    .isMobilePhone('zh-CN')
    .withMessage('手机号格式不正确'),
];

// 用户更新验证规则
const updateUserValidation = [
  body('name')
    .optional()
    .isLength({ min: 1, max: 100 })
    .withMessage('姓名长度必须在1-100个字符之间'),
  body('role')
    .optional()
    .isIn(Object.values(UserRole))
    .withMessage('无效的用户角色'),
  body('email')
    .optional()
    .isEmail()
    .withMessage('邮箱格式不正确'),
  body('phone')
    .optional()
    .isMobilePhone('zh-CN')
    .withMessage('手机号格式不正确'),
  body('status')
    .optional()
    .isIn(['active', 'inactive', 'suspended'])
    .withMessage('无效的用户状态'),
];

// 获取用户列表
router.get('/', requireAdmin, asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const {
    page = 1,
    pageSize = 20,
    sortBy = 'created_at',
    sortOrder = 'desc',
    search,
    role,
    status,
    department
  } = req.query;

  // 构建查询
  let query = db('users').select(
    'id', 'employee_id', 'name', 'role', 'department', 
    'email', 'phone', 'status', 'created_at', 'last_login_at'
  );

  // 搜索
  if (search) {
    query = QueryBuilder.search(query, search as string, ['name', 'employee_id', 'department']);
  }

  // 过滤
  const filters: Record<string, any> = {};
  if (role) filters.role = role;
  if (status) filters.status = status;
  if (department) filters.department = department;
  
  query = QueryBuilder.filter(query, filters);

  // 排序
  query = QueryBuilder.sort(query, sortBy as string, sortOrder as 'asc' | 'desc');

  // 获取总数
  const totalQuery = query.clone();
  const [{ count }] = await totalQuery.count('* as count');
  const total = parseInt(count as string);

  // 分页
  query = QueryBuilder.paginate(query, Number(page), Number(pageSize));

  // 执行查询
  const users = await query;

  const response: ApiResponse<PaginatedResponse<User>> = {
    success: true,
    message: '获取用户列表成功',
    data: {
      data: users,
      pagination: {
        page: Number(page),
        pageSize: Number(pageSize),
        total,
        totalPages: Math.ceil(total / Number(pageSize)),
      },
    },
    timestamp: new Date().toISOString(),
  };

  res.json(response);
}));

// 获取单个用户
router.get('/:id', checkResourceAccess('user'), asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;

  const user = await db('users')
    .select('id', 'employee_id', 'name', 'role', 'department', 'email', 'phone', 'status', 'created_at', 'last_login_at')
    .where({ id })
    .first();

  if (!user) {
    throw new NotFoundError('用户不存在');
  }

  const response: ApiResponse<{ user: User }> = {
    success: true,
    message: '获取用户信息成功',
    data: { user },
    timestamp: new Date().toISOString(),
  };

  res.json(response);
}));

// 创建用户
router.post('/', requireAdmin, createUserValidation, asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  // 验证输入
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new ValidationError('输入数据验证失败');
  }

  const { employee_id, name, role, department, email, phone, password } = req.body;

  // 检查工号是否已存在
  const existingUser = await db('users').where({ employee_id }).first();
  if (existingUser) {
    throw new ConflictError('工号已存在');
  }

  // 检查邮箱是否已存在
  if (email) {
    const existingEmail = await db('users').where({ email }).first();
    if (existingEmail) {
      throw new ConflictError('邮箱已存在');
    }
  }

  // 加密密码
  const password_hash = await bcrypt.hash(password, config.security.bcryptRounds);

  // 创建用户
  const [newUser] = await db('users')
    .insert({
      employee_id,
      name,
      role,
      department,
      email,
      phone,
      password_hash,
      status: 'active',
    })
    .returning(['id', 'employee_id', 'name', 'role', 'department', 'email', 'phone', 'status', 'created_at']);

  logger.info('用户创建成功:', {
    createdBy: req.user?.id,
    newUserId: newUser.id,
    employeeId: employee_id,
    role,
  });

  const response: ApiResponse<{ user: User }> = {
    success: true,
    message: '用户创建成功',
    data: { user: newUser },
    timestamp: new Date().toISOString(),
  };

  res.status(201).json(response);
}));

// 更新用户
router.put('/:id', checkResourceAccess('user'), updateUserValidation, asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  // 验证输入
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new ValidationError('输入数据验证失败');
  }

  const { id } = req.params;
  const { name, role, department, email, phone, status } = req.body;

  // 检查用户是否存在
  const existingUser = await db('users').where({ id }).first();
  if (!existingUser) {
    throw new NotFoundError('用户不存在');
  }

  // 检查权限：只有超级管理员可以修改角色
  if (role && role !== existingUser.role && req.user?.role !== UserRole.SUPER_ADMIN) {
    throw new ValidationError('权限不足，无法修改用户角色');
  }

  // 检查邮箱是否已被其他用户使用
  if (email && email !== existingUser.email) {
    const existingEmail = await db('users').where({ email }).whereNot({ id }).first();
    if (existingEmail) {
      throw new ConflictError('邮箱已被其他用户使用');
    }
  }

  // 更新用户信息
  const updateData: Partial<User> = {
    updated_at: new Date(),
  };

  if (name !== undefined) updateData.name = name;
  if (role !== undefined) updateData.role = role;
  if (department !== undefined) updateData.department = department;
  if (email !== undefined) updateData.email = email;
  if (phone !== undefined) updateData.phone = phone;
  if (status !== undefined) updateData.status = status;

  const [updatedUser] = await db('users')
    .where({ id })
    .update(updateData)
    .returning(['id', 'employee_id', 'name', 'role', 'department', 'email', 'phone', 'status', 'updated_at']);

  logger.info('用户更新成功:', {
    updatedBy: req.user?.id,
    userId: id,
    changes: updateData,
  });

  const response: ApiResponse<{ user: User }> = {
    success: true,
    message: '用户更新成功',
    data: { user: updatedUser },
    timestamp: new Date().toISOString(),
  };

  res.json(response);
}));

// 删除用户
router.delete('/:id', requireSuperAdmin, asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;

  // 检查用户是否存在
  const existingUser = await db('users').where({ id }).first();
  if (!existingUser) {
    throw new NotFoundError('用户不存在');
  }

  // 不能删除自己
  if (req.user?.id === parseInt(id)) {
    throw new ValidationError('不能删除自己的账户');
  }

  // 软删除：将状态设置为 inactive
  await db('users')
    .where({ id })
    .update({ 
      status: 'inactive',
      updated_at: new Date()
    });

  logger.info('用户删除成功:', {
    deletedBy: req.user?.id,
    userId: id,
    employeeId: existingUser.employee_id,
  });

  const response: ApiResponse = {
    success: true,
    message: '用户删除成功',
    timestamp: new Date().toISOString(),
  };

  res.json(response);
}));

export default router;
