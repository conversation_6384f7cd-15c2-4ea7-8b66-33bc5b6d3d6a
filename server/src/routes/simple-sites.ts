import { Router, Response } from 'express';
import { query, body, validationResult } from 'express-validator';
import { db } from '../utils/database';
import { logger } from '../utils/logger';
import { authenticateToken } from '../middleware/simple-auth';
import { 
  asyncHandler, 
  ValidationError, 
  NotFoundError 
} from '../middleware/errorHandler';
import { AuthenticatedRequest, ApiResponse } from '../types';

const router = Router();

// 获取站址列表
router.get('/', 
  authenticateToken,
  [
    query('page').optional().isInt({ min: 1 }).withMessage('页码必须是正整数'),
    query('pageSize').optional().isInt({ min: 1, max: 100 }).withMessage('每页大小必须在1-100之间'),
    query('search').optional().isString().withMessage('搜索关键词必须是字符串'),
    query('site_type').optional().isString().withMessage('站址类型必须是字符串'),
    query('site_level').optional().isString().withMessage('站址等级必须是字符串'),
    query('status').optional().isString().withMessage('状态必须是字符串'),
    query('province').optional().isString().withMessage('省份必须是字符串'),
    query('city').optional().isString().withMessage('城市必须是字符串'),
    query('network_type').optional().isString().withMessage('网络类型必须是字符串'),
    query('is_key_site').optional().isBoolean().withMessage('重点站址标识必须是布尔值'),
    query('sortBy').optional().isString().withMessage('排序字段必须是字符串'),
    query('sortOrder').optional().isIn(['asc', 'desc']).withMessage('排序方向必须是asc或desc'),
  ],
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    // 验证输入
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError('输入数据验证失败');
    }

    const {
      page = 1,
      pageSize = 20,
      sortBy = 'created_at',
      sortOrder = 'desc',
      search,
      site_type,
      site_level,
      status,
      province,
      city,
      network_type,
      is_key_site
    } = req.query;

    // 构建查询
    let query = db('sites').select('*');

    // 搜索条件
    if (search) {
      query = query.where(function() {
        this.where('site_code', 'like', `%${search}%`)
          .orWhere('site_name', 'like', `%${search}%`)
          .orWhere('address', 'like', `%${search}%`)
          .orWhere('responsible_person', 'like', `%${search}%`);
      });
    }

    // 筛选条件
    if (site_type) {
      query = query.where('site_type', site_type);
    }
    if (site_level) {
      query = query.where('site_level', site_level);
    }
    if (status) {
      query = query.where('status', status);
    }
    if (province) {
      query = query.where('province', province);
    }
    if (city) {
      query = query.where('city', city);
    }
    if (network_type) {
      query = query.where('network_type', network_type);
    }
    if (is_key_site !== undefined) {
      query = query.where('is_key_site', is_key_site === 'true' ? 1 : 0);
    }

    // 获取总数
    const totalQuery = query.clone();
    const [{ count }] = await totalQuery.count('* as count');
    const total = parseInt(count as string);

    // 排序和分页
    const validSortFields = [
      'site_code', 'site_name', 'site_type', 'site_level', 'status',
      'province', 'city', 'created_at', 'updated_at', 'next_maintenance_date'
    ];
    
    const sortField = validSortFields.includes(sortBy as string) ? sortBy as string : 'created_at';
    query = query.orderBy(sortField, sortOrder as 'asc' | 'desc');

    const offset = (Number(page) - 1) * Number(pageSize);
    query = query.limit(Number(pageSize)).offset(offset);

    const sites = await query;

    const response: ApiResponse = {
      success: true,
      message: '获取站址列表成功',
      data: {
        sites,
        pagination: {
          page: Number(page),
          pageSize: Number(pageSize),
          total,
          totalPages: Math.ceil(total / Number(pageSize)),
        },
      },
      timestamp: new Date().toISOString(),
    };

    res.json(response);
  })
);

// 获取站址详情
router.get('/:id', 
  authenticateToken,
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const { id } = req.params;

    const site = await db('sites')
      .where({ id })
      .first();

    if (!site) {
      throw new NotFoundError('站址不存在');
    }

    const response: ApiResponse = {
      success: true,
      message: '获取站址详情成功',
      data: { site },
      timestamp: new Date().toISOString(),
    };

    res.json(response);
  })
);

// 获取站址统计信息
router.get('/statistics/overview', 
  authenticateToken,
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    // 基础统计
    const [totalSites] = await db('sites').count('* as count');
    const [activeSites] = await db('sites').where('status', 'normal').count('* as count');
    const [keySites] = await db('sites').where('is_key_site', 1).count('* as count');
    const [maintenanceSites] = await db('sites').where('status', 'maintenance').count('* as count');

    // 待维护站址（30天内需要维护）
    const thirtyDaysFromNow = new Date();
    thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);
    
    const [pendingMaintenanceSites] = await db('sites')
      .where('next_maintenance_date', '<=', thirtyDaysFromNow.toISOString())
      .where('status', '!=', 'decommissioned')
      .count('* as count');

    // 状态分布
    const statusDistribution = await db('sites')
      .select('status')
      .count('* as count')
      .groupBy('status');

    // 类型分布
    const typeDistribution = await db('sites')
      .select('site_type')
      .count('* as count')
      .groupBy('site_type');

    // 等级分布
    const levelDistribution = await db('sites')
      .select('site_level')
      .count('* as count')
      .groupBy('site_level');

    const response: ApiResponse = {
      success: true,
      message: '获取站址统计信息成功',
      data: {
        overview: {
          totalSites: parseInt(totalSites.count as string),
          activeSites: parseInt(activeSites.count as string),
          keySites: parseInt(keySites.count as string),
          maintenanceSites: parseInt(maintenanceSites.count as string),
          pendingMaintenanceSites: parseInt(pendingMaintenanceSites.count as string),
        },
        distributions: {
          status: statusDistribution.map(item => ({
            name: item.status,
            count: parseInt(item.count as string)
          })),
          type: typeDistribution.map(item => ({
            name: item.site_type,
            count: parseInt(item.count as string)
          })),
          level: levelDistribution.map(item => ({
            name: item.site_level,
            count: parseInt(item.count as string)
          })),
        },
      },
      timestamp: new Date().toISOString(),
    };

    res.json(response);
  })
);

export default router;
