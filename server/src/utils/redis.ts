import { createClient, RedisClientType } from 'redis';
import { config } from '../config';
import { logger } from './logger';

class RedisClient {
  private client: RedisClientType;
  private isConnected: boolean = false;

  constructor() {
    this.client = createClient({
      socket: {
        host: config.redis.host,
        port: config.redis.port,
      },
      password: config.redis.password || undefined,
    });

    this.setupEventHandlers();
  }

  private setupEventHandlers(): void {
    this.client.on('connect', () => {
      logger.info('Redis 客户端连接中...');
    });

    this.client.on('ready', () => {
      logger.info('Redis 客户端连接成功');
      this.isConnected = true;
    });

    this.client.on('error', (error) => {
      logger.error('Redis 客户端错误:', error);
      this.isConnected = false;
    });

    this.client.on('end', () => {
      logger.info('Redis 客户端连接关闭');
      this.isConnected = false;
    });
  }

  public async connect(): Promise<void> {
    try {
      await this.client.connect();
    } catch (error) {
      logger.error('Redis 连接失败:', error);
      throw error;
    }
  }

  public async disconnect(): Promise<void> {
    try {
      await this.client.disconnect();
    } catch (error) {
      logger.error('Redis 断开连接失败:', error);
      throw error;
    }
  }

  public isReady(): boolean {
    return this.isConnected;
  }

  // 基础操作
  public async set(key: string, value: string, ttl?: number): Promise<void> {
    try {
      if (ttl) {
        await this.client.setEx(key, ttl, value);
      } else {
        await this.client.set(key, value);
      }
    } catch (error) {
      logger.error('Redis SET 操作失败:', { key, error });
      throw error;
    }
  }

  public async get(key: string): Promise<string | null> {
    try {
      return await this.client.get(key);
    } catch (error) {
      logger.error('Redis GET 操作失败:', { key, error });
      throw error;
    }
  }

  public async del(key: string): Promise<number> {
    try {
      return await this.client.del(key);
    } catch (error) {
      logger.error('Redis DEL 操作失败:', { key, error });
      throw error;
    }
  }

  public async exists(key: string): Promise<number> {
    try {
      return await this.client.exists(key);
    } catch (error) {
      logger.error('Redis EXISTS 操作失败:', { key, error });
      throw error;
    }
  }

  public async expire(key: string, seconds: number): Promise<boolean> {
    try {
      return await this.client.expire(key, seconds);
    } catch (error) {
      logger.error('Redis EXPIRE 操作失败:', { key, seconds, error });
      throw error;
    }
  }

  // JSON 操作
  public async setJSON(key: string, value: any, ttl?: number): Promise<void> {
    const jsonString = JSON.stringify(value);
    await this.set(key, jsonString, ttl);
  }

  public async getJSON<T>(key: string): Promise<T | null> {
    const jsonString = await this.get(key);
    if (!jsonString) return null;
    
    try {
      return JSON.parse(jsonString) as T;
    } catch (error) {
      logger.error('JSON 解析失败:', { key, error });
      return null;
    }
  }

  // Hash 操作
  public async hSet(key: string, field: string, value: string): Promise<number> {
    try {
      return await this.client.hSet(key, field, value);
    } catch (error) {
      logger.error('Redis HSET 操作失败:', { key, field, error });
      throw error;
    }
  }

  public async hGet(key: string, field: string): Promise<string | undefined> {
    try {
      return await this.client.hGet(key, field);
    } catch (error) {
      logger.error('Redis HGET 操作失败:', { key, field, error });
      throw error;
    }
  }

  public async hGetAll(key: string): Promise<Record<string, string>> {
    try {
      return await this.client.hGetAll(key);
    } catch (error) {
      logger.error('Redis HGETALL 操作失败:', { key, error });
      throw error;
    }
  }

  public async hDel(key: string, field: string): Promise<number> {
    try {
      return await this.client.hDel(key, field);
    } catch (error) {
      logger.error('Redis HDEL 操作失败:', { key, field, error });
      throw error;
    }
  }

  // List 操作
  public async lPush(key: string, ...values: string[]): Promise<number> {
    try {
      return await this.client.lPush(key, values);
    } catch (error) {
      logger.error('Redis LPUSH 操作失败:', { key, error });
      throw error;
    }
  }

  public async rPop(key: string): Promise<string | null> {
    try {
      return await this.client.rPop(key);
    } catch (error) {
      logger.error('Redis RPOP 操作失败:', { key, error });
      throw error;
    }
  }

  public async lRange(key: string, start: number, stop: number): Promise<string[]> {
    try {
      return await this.client.lRange(key, start, stop);
    } catch (error) {
      logger.error('Redis LRANGE 操作失败:', { key, start, stop, error });
      throw error;
    }
  }

  // Set 操作
  public async sAdd(key: string, ...members: string[]): Promise<number> {
    try {
      return await this.client.sAdd(key, members);
    } catch (error) {
      logger.error('Redis SADD 操作失败:', { key, error });
      throw error;
    }
  }

  public async sMembers(key: string): Promise<string[]> {
    try {
      return await this.client.sMembers(key);
    } catch (error) {
      logger.error('Redis SMEMBERS 操作失败:', { key, error });
      throw error;
    }
  }

  public async sRem(key: string, ...members: string[]): Promise<number> {
    try {
      return await this.client.sRem(key, members);
    } catch (error) {
      logger.error('Redis SREM 操作失败:', { key, error });
      throw error;
    }
  }

  // 缓存辅助方法
  public async cache<T>(
    key: string,
    fetcher: () => Promise<T>,
    ttl: number = 3600
  ): Promise<T> {
    // 尝试从缓存获取
    const cached = await this.getJSON<T>(key);
    if (cached !== null) {
      return cached;
    }

    // 缓存未命中，执行获取函数
    const data = await fetcher();
    
    // 存储到缓存
    await this.setJSON(key, data, ttl);
    
    return data;
  }
}

// 创建全局 Redis 客户端实例
export const redis = new RedisClient();

// 初始化 Redis 连接
export async function initializeRedis(): Promise<void> {
  try {
    await redis.connect();
    logger.info('Redis 初始化完成');
  } catch (error) {
    logger.error('Redis 初始化失败:', error);
    throw error;
  }
}

// 关闭 Redis 连接
export async function closeRedis(): Promise<void> {
  try {
    await redis.disconnect();
    logger.info('Redis 连接已关闭');
  } catch (error) {
    logger.error('关闭 Redis 连接失败:', error);
    throw error;
  }
}
