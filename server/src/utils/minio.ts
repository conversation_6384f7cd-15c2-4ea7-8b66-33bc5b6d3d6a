import * as Minio from 'minio';
import { config } from '../config';
import { logger } from './logger';

class MinIOClient {
  private client: Minio.Client;
  private bucketName: string;

  constructor() {
    this.client = new Minio.Client({
      endPoint: config.minio.endpoint,
      port: config.minio.port,
      useSSL: config.minio.useSSL,
      accessKey: config.minio.accessKey,
      secretKey: config.minio.secretKey,
    });
    
    this.bucketName = config.minio.bucketName;
  }

  // 初始化 MinIO
  public async initialize(): Promise<void> {
    try {
      // 检查存储桶是否存在
      const bucketExists = await this.client.bucketExists(this.bucketName);
      
      if (!bucketExists) {
        // 创建存储桶
        await this.client.makeBucket(this.bucketName, 'us-east-1');
        logger.info(`MinIO 存储桶创建成功: ${this.bucketName}`);
        
        // 设置存储桶策略（允许读取）
        const policy = {
          Version: '2012-10-17',
          Statement: [
            {
              Effect: 'Allow',
              Principal: { AWS: ['*'] },
              Action: ['s3:GetObject'],
              Resource: [`arn:aws:s3:::${this.bucketName}/*`],
            },
          ],
        };
        
        await this.client.setBucketPolicy(this.bucketName, JSON.stringify(policy));
        logger.info(`MinIO 存储桶策略设置成功: ${this.bucketName}`);
      } else {
        logger.info(`MinIO 存储桶已存在: ${this.bucketName}`);
      }
    } catch (error) {
      logger.error('MinIO 初始化失败:', error);
      throw error;
    }
  }

  // 上传文件
  public async uploadFile(
    objectName: string,
    filePath: string,
    metaData?: Record<string, any>
  ): Promise<Minio.UploadedObjectInfo> {
    try {
      const result = await this.client.fPutObject(
        this.bucketName,
        objectName,
        filePath,
        metaData
      );
      
      logger.info('文件上传成功:', {
        objectName,
        filePath,
        etag: result.etag,
      });
      
      return result;
    } catch (error) {
      logger.error('文件上传失败:', { objectName, filePath, error });
      throw error;
    }
  }

  // 上传文件流
  public async uploadStream(
    objectName: string,
    stream: NodeJS.ReadableStream,
    size?: number,
    metaData?: Record<string, any>
  ): Promise<Minio.UploadedObjectInfo> {
    try {
      const result = await this.client.putObject(
        this.bucketName,
        objectName,
        stream,
        size,
        metaData
      );
      
      logger.info('文件流上传成功:', {
        objectName,
        size,
        etag: result.etag,
      });
      
      return result;
    } catch (error) {
      logger.error('文件流上传失败:', { objectName, error });
      throw error;
    }
  }

  // 下载文件
  public async downloadFile(objectName: string, filePath: string): Promise<void> {
    try {
      await this.client.fGetObject(this.bucketName, objectName, filePath);
      logger.info('文件下载成功:', { objectName, filePath });
    } catch (error) {
      logger.error('文件下载失败:', { objectName, filePath, error });
      throw error;
    }
  }

  // 获取文件流
  public async getFileStream(objectName: string): Promise<NodeJS.ReadableStream> {
    try {
      const stream = await this.client.getObject(this.bucketName, objectName);
      logger.info('获取文件流成功:', { objectName });
      return stream;
    } catch (error) {
      logger.error('获取文件流失败:', { objectName, error });
      throw error;
    }
  }

  // 删除文件
  public async deleteFile(objectName: string): Promise<void> {
    try {
      await this.client.removeObject(this.bucketName, objectName);
      logger.info('文件删除成功:', { objectName });
    } catch (error) {
      logger.error('文件删除失败:', { objectName, error });
      throw error;
    }
  }

  // 批量删除文件
  public async deleteFiles(objectNames: string[]): Promise<void> {
    try {
      await this.client.removeObjects(this.bucketName, objectNames);
      logger.info('批量删除文件成功:', { count: objectNames.length });
    } catch (error) {
      logger.error('批量删除文件失败:', { objectNames, error });
      throw error;
    }
  }

  // 获取文件信息
  public async getFileInfo(objectName: string): Promise<Minio.BucketItemStat> {
    try {
      const stat = await this.client.statObject(this.bucketName, objectName);
      logger.info('获取文件信息成功:', { objectName, size: stat.size });
      return stat;
    } catch (error) {
      logger.error('获取文件信息失败:', { objectName, error });
      throw error;
    }
  }

  // 列出文件
  public async listFiles(prefix?: string, recursive: boolean = false): Promise<Minio.BucketItem[]> {
    try {
      const objects: Minio.BucketItem[] = [];
      const stream = this.client.listObjects(this.bucketName, prefix, recursive);
      
      return new Promise((resolve, reject) => {
        stream.on('data', (obj) => objects.push(obj));
        stream.on('error', reject);
        stream.on('end', () => {
          logger.info('列出文件成功:', { count: objects.length, prefix });
          resolve(objects);
        });
      });
    } catch (error) {
      logger.error('列出文件失败:', { prefix, error });
      throw error;
    }
  }

  // 生成预签名 URL
  public async generatePresignedUrl(
    objectName: string,
    expiry: number = 7 * 24 * 60 * 60, // 7天
    reqParams?: Record<string, any>
  ): Promise<string> {
    try {
      const url = await this.client.presignedGetObject(
        this.bucketName,
        objectName,
        expiry,
        reqParams
      );
      
      logger.info('生成预签名URL成功:', { objectName, expiry });
      return url;
    } catch (error) {
      logger.error('生成预签名URL失败:', { objectName, error });
      throw error;
    }
  }

  // 生成上传预签名 URL
  public async generateUploadUrl(
    objectName: string,
    expiry: number = 60 * 60 // 1小时
  ): Promise<string> {
    try {
      const url = await this.client.presignedPutObject(
        this.bucketName,
        objectName,
        expiry
      );
      
      logger.info('生成上传预签名URL成功:', { objectName, expiry });
      return url;
    } catch (error) {
      logger.error('生成上传预签名URL失败:', { objectName, error });
      throw error;
    }
  }

  // 检查文件是否存在
  public async fileExists(objectName: string): Promise<boolean> {
    try {
      await this.client.statObject(this.bucketName, objectName);
      return true;
    } catch (error) {
      if (error.code === 'NotFound') {
        return false;
      }
      logger.error('检查文件存在性失败:', { objectName, error });
      throw error;
    }
  }

  // 复制文件
  public async copyFile(
    sourceObjectName: string,
    destObjectName: string,
    conditions?: Minio.CopyConditions
  ): Promise<Minio.BucketItemCopy> {
    try {
      const copySource = new Minio.CopySourceOptions({
        Bucket: this.bucketName,
        Object: sourceObjectName,
      });
      
      const copyDest = new Minio.CopyDestinationOptions({
        Bucket: this.bucketName,
        Object: destObjectName,
      });
      
      const result = await this.client.copyObject(copyDest, copySource);
      logger.info('文件复制成功:', { sourceObjectName, destObjectName });
      return result;
    } catch (error) {
      logger.error('文件复制失败:', { sourceObjectName, destObjectName, error });
      throw error;
    }
  }

  // 获取存储桶名称
  public getBucketName(): string {
    return this.bucketName;
  }

  // 获取客户端实例
  public getClient(): Minio.Client {
    return this.client;
  }
}

// 创建全局 MinIO 客户端实例
export const minioClient = new MinIOClient();

// 初始化 MinIO
export async function initializeMinio(): Promise<void> {
  try {
    await minioClient.initialize();
    logger.info('MinIO 初始化完成');
  } catch (error) {
    logger.error('MinIO 初始化失败:', error);
    throw error;
  }
}
