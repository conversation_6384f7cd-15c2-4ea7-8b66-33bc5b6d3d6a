import knex, { Knex } from 'knex';
import { config } from '../config';
import { logger } from './logger';

// 数据库配置
const dbConfig: Knex.Config = {
  client: 'postgresql',
  connection: {
    host: config.database.host,
    port: config.database.port,
    database: config.database.name,
    user: config.database.user,
    password: config.database.password,
  },
  pool: {
    min: 2,
    max: 10,
    acquireTimeoutMillis: 30000,
    createTimeoutMillis: 30000,
    destroyTimeoutMillis: 5000,
    idleTimeoutMillis: 30000,
    reapIntervalMillis: 1000,
    createRetryIntervalMillis: 100,
  },
  migrations: {
    tableName: 'knex_migrations',
    directory: '../database/migrations',
  },
  seeds: {
    directory: '../database/seeds',
  },
};

// 创建数据库连接实例
export const db = knex(dbConfig);

// 初始化数据库
export async function initializeDatabase(): Promise<void> {
  try {
    // 测试数据库连接
    await db.raw('SELECT 1');
    logger.info('数据库连接成功');

    // 运行迁移
    const [batchNo, migrations] = await db.migrate.latest();
    if (migrations.length > 0) {
      logger.info(`运行了 ${migrations.length} 个迁移文件，批次号: ${batchNo}`);
      migrations.forEach((migration: any) => {
        logger.info(`迁移文件: ${migration}`);
      });
    } else {
      logger.info('数据库已是最新版本，无需迁移');
    }

  } catch (error) {
    logger.error('数据库初始化失败:', error);
    throw error;
  }
}

// 关闭数据库连接
export async function closeDatabase(): Promise<void> {
  try {
    await db.destroy();
    logger.info('数据库连接已关闭');
  } catch (error) {
    logger.error('关闭数据库连接失败:', error);
    throw error;
  }
}

// 数据库健康检查
export async function checkDatabaseHealth(): Promise<boolean> {
  try {
    await db.raw('SELECT 1');
    return true;
  } catch (error) {
    logger.error('数据库健康检查失败:', error);
    return false;
  }
}

// 通用查询构建器
export class QueryBuilder {
  static paginate<T>(
    query: Knex.QueryBuilder,
    page: number = 1,
    pageSize: number = 20
  ) {
    const offset = (page - 1) * pageSize;
    return query.offset(offset).limit(pageSize);
  }

  static search<T>(
    query: Knex.QueryBuilder,
    searchTerm: string,
    searchFields: string[]
  ) {
    if (!searchTerm || searchFields.length === 0) {
      return query;
    }

    return query.where((builder) => {
      searchFields.forEach((field, index) => {
        if (index === 0) {
          builder.where(field, 'ILIKE', `%${searchTerm}%`);
        } else {
          builder.orWhere(field, 'ILIKE', `%${searchTerm}%`);
        }
      });
    });
  }

  static sort<T>(
    query: Knex.QueryBuilder,
    sortBy: string = 'created_at',
    sortOrder: 'asc' | 'desc' = 'desc'
  ) {
    return query.orderBy(sortBy, sortOrder);
  }

  static filter<T>(
    query: Knex.QueryBuilder,
    filters: Record<string, any>
  ) {
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        if (Array.isArray(value)) {
          query.whereIn(key, value);
        } else if (typeof value === 'object' && value.operator) {
          // 支持复杂查询条件
          const { operator, value: filterValue } = value;
          switch (operator) {
            case 'gte':
              query.where(key, '>=', filterValue);
              break;
            case 'lte':
              query.where(key, '<=', filterValue);
              break;
            case 'gt':
              query.where(key, '>', filterValue);
              break;
            case 'lt':
              query.where(key, '<', filterValue);
              break;
            case 'like':
              query.where(key, 'ILIKE', `%${filterValue}%`);
              break;
            case 'between':
              query.whereBetween(key, filterValue);
              break;
            default:
              query.where(key, filterValue);
          }
        } else {
          query.where(key, value);
        }
      }
    });
    return query;
  }
}

// 事务处理辅助函数
export async function withTransaction<T>(
  callback: (trx: Knex.Transaction) => Promise<T>
): Promise<T> {
  const trx = await db.transaction();
  try {
    const result = await callback(trx);
    await trx.commit();
    return result;
  } catch (error) {
    await trx.rollback();
    throw error;
  }
}
