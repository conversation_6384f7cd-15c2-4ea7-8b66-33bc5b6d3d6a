import mqtt, { MqttClient } from 'mqtt';
import { Server as SocketIOServer } from 'socket.io';
import { config } from '../config';
import { logger } from './logger';
import { db } from './database';
import { MqttMessage } from '../types';

class MQTTClient {
  private client: MqttClient | null = null;
  private io: SocketIOServer | null = null;
  private isConnected: boolean = false;

  // MQTT 主题定义
  private topics = {
    deviceHeartbeat: 'device/+/heartbeat',
    deviceStatus: 'device/+/status',
    taskSync: 'device/+/task/sync',
    dataUpload: 'device/+/data/upload',
    mediaUpload: 'device/+/media/upload',
    deviceConfig: 'device/+/config',
    systemBroadcast: 'system/broadcast',
  };

  constructor() {}

  // 初始化 MQTT 客户端
  public async initialize(io: SocketIOServer): Promise<void> {
    this.io = io;

    const clientOptions = {
      host: config.mqtt.host,
      port: config.mqtt.port,
      clientId: config.mqtt.clientId,
      username: config.mqtt.username || undefined,
      password: config.mqtt.password || undefined,
      clean: true,
      reconnectPeriod: 5000,
      connectTimeout: 30000,
    };

    try {
      this.client = mqtt.connect(clientOptions);
      this.setupEventHandlers();
      
      return new Promise((resolve, reject) => {
        if (!this.client) {
          reject(new Error('MQTT 客户端创建失败'));
          return;
        }

        this.client.on('connect', () => {
          logger.info('MQTT 客户端连接成功');
          this.isConnected = true;
          this.subscribeToTopics();
          resolve();
        });

        this.client.on('error', (error) => {
          logger.error('MQTT 连接失败:', error);
          reject(error);
        });

        // 设置超时
        setTimeout(() => {
          if (!this.isConnected) {
            reject(new Error('MQTT 连接超时'));
          }
        }, 30000);
      });
    } catch (error) {
      logger.error('MQTT 初始化失败:', error);
      throw error;
    }
  }

  // 设置事件处理器
  private setupEventHandlers(): void {
    if (!this.client) return;

    this.client.on('connect', () => {
      logger.info('MQTT 客户端已连接');
      this.isConnected = true;
    });

    this.client.on('disconnect', () => {
      logger.warn('MQTT 客户端断开连接');
      this.isConnected = false;
    });

    this.client.on('error', (error) => {
      logger.error('MQTT 客户端错误:', error);
      this.isConnected = false;
    });

    this.client.on('message', (topic, message) => {
      this.handleMessage(topic, message);
    });

    this.client.on('reconnect', () => {
      logger.info('MQTT 客户端重连中...');
    });

    this.client.on('offline', () => {
      logger.warn('MQTT 客户端离线');
      this.isConnected = false;
    });
  }

  // 订阅主题
  private subscribeToTopics(): void {
    if (!this.client) return;

    Object.values(this.topics).forEach((topic) => {
      this.client!.subscribe(topic, (error) => {
        if (error) {
          logger.error(`订阅主题失败: ${topic}`, error);
        } else {
          logger.info(`订阅主题成功: ${topic}`);
        }
      });
    });
  }

  // 处理接收到的消息
  private async handleMessage(topic: string, message: Buffer): Promise<void> {
    try {
      const messageStr = message.toString();
      const deviceId = this.extractDeviceId(topic);
      
      logger.info('收到 MQTT 消息:', { topic, deviceId, messageLength: messageStr.length });

      let parsedMessage: any;
      try {
        parsedMessage = JSON.parse(messageStr);
      } catch (error) {
        logger.error('MQTT 消息解析失败:', { topic, message: messageStr });
        return;
      }

      const mqttMessage: MqttMessage = {
        deviceId,
        messageType: this.getMessageType(topic),
        payload: parsedMessage,
        timestamp: new Date(),
      };

      // 根据消息类型处理
      await this.processMessage(mqttMessage);

      // 通过 WebSocket 广播给前端
      this.broadcastToWebSocket(mqttMessage);

    } catch (error) {
      logger.error('处理 MQTT 消息失败:', { topic, error });
    }
  }

  // 从主题中提取设备ID
  private extractDeviceId(topic: string): string {
    const parts = topic.split('/');
    return parts[1] || 'unknown';
  }

  // 获取消息类型
  private getMessageType(topic: string): string {
    if (topic.includes('heartbeat')) return 'heartbeat';
    if (topic.includes('status')) return 'status';
    if (topic.includes('task')) return 'task';
    if (topic.includes('data')) return 'data';
    if (topic.includes('media')) return 'media';
    if (topic.includes('config')) return 'config';
    return 'unknown';
  }

  // 处理不同类型的消息
  private async processMessage(message: MqttMessage): Promise<void> {
    const { deviceId, messageType, payload } = message;

    switch (messageType) {
      case 'heartbeat':
        await this.handleHeartbeat(deviceId, payload);
        break;
      case 'status':
        await this.handleStatusUpdate(deviceId, payload);
        break;
      case 'data':
        await this.handleDataUpload(deviceId, payload);
        break;
      case 'media':
        await this.handleMediaUpload(deviceId, payload);
        break;
      default:
        logger.warn('未知的消息类型:', { messageType, deviceId });
    }
  }

  // 处理设备心跳
  private async handleHeartbeat(deviceId: string, payload: any): Promise<void> {
    try {
      await db('devices')
        .where({ device_id: deviceId })
        .update({
          status: 'online',
          battery_level: payload.batteryLevel,
          last_heartbeat: new Date(),
          updated_at: new Date(),
        });

      logger.debug('设备心跳更新成功:', { deviceId, batteryLevel: payload.batteryLevel });
    } catch (error) {
      logger.error('处理设备心跳失败:', { deviceId, error });
    }
  }

  // 处理设备状态更新
  private async handleStatusUpdate(deviceId: string, payload: any): Promise<void> {
    try {
      await db('devices')
        .where({ device_id: deviceId })
        .update({
          status: payload.status,
          firmware_version: payload.firmwareVersion,
          device_config: payload.config,
          updated_at: new Date(),
        });

      logger.info('设备状态更新成功:', { deviceId, status: payload.status });
    } catch (error) {
      logger.error('处理设备状态更新失败:', { deviceId, error });
    }
  }

  // 处理数据上传
  private async handleDataUpload(deviceId: string, payload: any): Promise<void> {
    try {
      // 这里应该处理巡检数据的存储
      // 具体实现会在后续的数据处理模块中完成
      logger.info('收到巡检数据:', { deviceId, dataType: payload.type });
    } catch (error) {
      logger.error('处理数据上传失败:', { deviceId, error });
    }
  }

  // 处理媒体文件上传
  private async handleMediaUpload(deviceId: string, payload: any): Promise<void> {
    try {
      // 这里应该处理媒体文件的存储
      // 具体实现会在后续的文件处理模块中完成
      logger.info('收到媒体文件:', { deviceId, fileType: payload.type });
    } catch (error) {
      logger.error('处理媒体上传失败:', { deviceId, error });
    }
  }

  // 通过 WebSocket 广播消息
  private broadcastToWebSocket(message: MqttMessage): void {
    if (!this.io) return;

    // 广播给所有连接的客户端
    this.io.emit('mqtt_message', message);

    // 广播给特定设备房间
    this.io.to(`device_${message.deviceId}`).emit('device_message', message);

    // 根据消息类型广播给不同房间
    switch (message.messageType) {
      case 'heartbeat':
        this.io.to('device_monitor').emit('device_heartbeat', message);
        break;
      case 'status':
        this.io.to('device_monitor').emit('device_status', message);
        break;
      case 'data':
        this.io.to('inspection_monitor').emit('inspection_data', message);
        break;
    }
  }

  // 发布消息
  public publish(topic: string, message: any): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.client || !this.isConnected) {
        reject(new Error('MQTT 客户端未连接'));
        return;
      }

      const messageStr = typeof message === 'string' ? message : JSON.stringify(message);

      this.client.publish(topic, messageStr, (error) => {
        if (error) {
          logger.error('发布 MQTT 消息失败:', { topic, error });
          reject(error);
        } else {
          logger.info('发布 MQTT 消息成功:', { topic });
          resolve();
        }
      });
    });
  }

  // 向设备发送任务
  public async sendTaskToDevice(deviceId: string, task: any): Promise<void> {
    const topic = `device/${deviceId}/task/assign`;
    await this.publish(topic, task);
  }

  // 向设备发送配置
  public async sendConfigToDevice(deviceId: string, config: any): Promise<void> {
    const topic = `device/${deviceId}/config/update`;
    await this.publish(topic, config);
  }

  // 系统广播
  public async broadcastMessage(message: any): Promise<void> {
    await this.publish(this.topics.systemBroadcast, message);
  }

  // 检查连接状态
  public isConnected(): boolean {
    return this.isConnected;
  }

  // 断开连接
  public async disconnect(): Promise<void> {
    if (this.client) {
      await new Promise<void>((resolve) => {
        this.client!.end(false, {}, () => {
          logger.info('MQTT 客户端已断开连接');
          resolve();
        });
      });
    }
  }
}

// 创建全局 MQTT 客户端实例
export const mqttClient = new MQTTClient();

// 初始化 MQTT
export async function initializeMqtt(io: SocketIOServer): Promise<void> {
  try {
    await mqttClient.initialize(io);
    logger.info('MQTT 初始化完成');
  } catch (error) {
    logger.error('MQTT 初始化失败:', error);
    throw error;
  }
}
