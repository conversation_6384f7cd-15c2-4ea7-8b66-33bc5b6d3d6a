{"name": "inspection-server", "version": "1.0.0", "description": "站址巡检系统后端服务", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "db:migrate": "knex migrate:latest", "db:seed": "knex seed:run", "db:rollback": "knex migrate:rollback"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "compression": "^1.7.4", "dotenv": "^16.3.1", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "joi": "^17.11.0", "knex": "^3.0.1", "pg": "^8.11.3", "redis": "^4.6.10", "minio": "^7.1.3", "mqtt": "^5.2.2", "socket.io": "^4.7.4", "multer": "^1.4.5-lts.1", "winston": "^3.11.0", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "uuid": "^9.0.1", "moment": "^2.29.4"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/morgan": "^1.9.9", "@types/compression": "^1.7.5", "@types/jsonwebtoken": "^9.0.5", "@types/bcryptjs": "^2.4.6", "@types/multer": "^1.4.11", "@types/uuid": "^9.0.7", "@types/node": "^20.10.4", "@typescript-eslint/eslint-plugin": "^6.13.2", "@typescript-eslint/parser": "^6.13.2", "eslint": "^8.55.0", "jest": "^29.7.0", "@types/jest": "^29.5.8", "ts-jest": "^29.1.1", "nodemon": "^3.0.2", "ts-node": "^10.9.1", "typescript": "^5.3.3"}}