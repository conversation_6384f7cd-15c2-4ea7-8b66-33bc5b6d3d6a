{"name": "inspection-server", "version": "1.0.0", "description": "站址巡检系统后端服务", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "db:migrate": "knex migrate:latest", "db:seed": "knex seed:run", "db:rollback": "knex migrate:rollback"}, "dependencies": {"bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "knex": "^3.0.1", "minio": "^7.1.3", "moment": "^2.29.4", "morgan": "^1.10.0", "mqtt": "^5.2.2", "multer": "^1.4.5-lts.1", "pg": "^8.11.3", "redis": "^4.6.10", "socket.io": "^4.7.4", "sqlite3": "^5.1.7", "uuid": "^9.0.1", "winston": "^3.11.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/jsonwebtoken": "^9.0.5", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.11", "@types/node": "^20.10.4", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.13.2", "@typescript-eslint/parser": "^6.13.2", "@vitejs/plugin-react": "^4.6.0", "eslint": "^8.55.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "typescript": "^5.3.3", "vite": "^7.0.4"}}