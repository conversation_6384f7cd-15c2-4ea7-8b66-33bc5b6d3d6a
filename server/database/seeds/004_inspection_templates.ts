import { Knex } from 'knex';

export async function seed(knex: Knex): Promise<void> {
  // 清空现有数据
  await knex('inspection_items').del();
  await knex('inspection_templates').del();

  // 插入模板数据
  const templates = [
    {
      id: 1,
      template_name: '5G基站标准巡检模板',
      template_code: 'TPL-5G-001',
      site_type: '5G基站',
      description: '适用于5G基站的标准巡检流程，包含设备检查、环境监测、性能测试等项目',
      version: 'v2.1',
      status: 'active',
      created_by: 1,
      created_at: new Date('2024-01-15T08:00:00Z'),
      updated_at: new Date('2024-01-20T10:30:00Z')
    },
    {
      id: 2,
      template_name: '4G基站常规巡检模板',
      template_code: 'TPL-4G-001',
      site_type: '4G基站',
      description: '4G基站日常巡检模板，重点关注设备运行状态和信号质量',
      version: 'v1.5',
      status: 'active',
      created_by: 1,
      created_at: new Date('2024-01-10T09:15:00Z'),
      updated_at: new Date('2024-01-18T14:20:00Z')
    },
    {
      id: 3,
      template_name: '微基站快速巡检模板',
      template_code: 'TPL-MICRO-001',
      site_type: '微基站',
      description: '微基站快速巡检模板，适用于密集部署区域的快速检查',
      version: 'v1.0',
      status: 'draft',
      created_by: 1,
      created_at: new Date('2024-01-22T11:00:00Z'),
      updated_at: new Date('2024-01-22T11:00:00Z')
    }
  ];

  await knex('inspection_templates').insert(templates);

  // 插入检查项目数据
  const checkItems = [
    // 5G基站模板检查项目
    {
      template_id: 1,
      step_number: 1,
      item_name: '电源系统检查',
      item_description: '检查电源设备运行状态，包括UPS、开关电源等',
      check_standard: '设备指示灯正常，无异响，温度正常',
      operation_guide: '1. 观察设备指示灯状态\n2. 听设备运行声音\n3. 检查温度显示',
      validation_rules: JSON.stringify({
        required: true,
        type: 'select',
        options: ['正常', '异常', '需维护']
      }),
      require_photo: true,
      require_video: false,
      auto_capture: false,
      voice_prompts: JSON.stringify(['请检查电源系统状态', '请拍摄设备照片']),
      exception_keywords: JSON.stringify(['异常', '故障', '告警', '高温']),
      data_type: 'select',
      options: JSON.stringify(['正常', '异常', '需维护']),
      is_required: true,
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      template_id: 1,
      step_number: 2,
      item_name: '天线系统检查',
      item_description: '检查天线安装状态、连接线缆等',
      check_standard: '天线安装牢固，线缆连接良好，无破损',
      operation_guide: '1. 检查天线安装是否牢固\n2. 检查线缆连接\n3. 查看是否有破损',
      validation_rules: JSON.stringify({
        required: true,
        type: 'select',
        options: ['正常', '异常', '需维护']
      }),
      require_photo: true,
      require_video: false,
      auto_capture: false,
      voice_prompts: JSON.stringify(['请检查天线系统', '请拍摄天线照片']),
      exception_keywords: JSON.stringify(['松动', '破损', '腐蚀', '断裂']),
      data_type: 'select',
      options: JSON.stringify(['正常', '异常', '需维护']),
      is_required: true,
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      template_id: 1,
      step_number: 3,
      item_name: '环境温湿度测量',
      item_description: '测量机房温度和湿度',
      check_standard: '温度18-28℃，湿度40-70%',
      operation_guide: '1. 使用温湿度计测量\n2. 记录数值\n3. 判断是否在标准范围内',
      validation_rules: JSON.stringify({
        required: true,
        type: 'number',
        min: -10,
        max: 50
      }),
      require_photo: false,
      require_video: false,
      auto_capture: true,
      voice_prompts: JSON.stringify(['请测量环境温度', '请记录温度数值']),
      exception_keywords: JSON.stringify(['超标', '过高', '过低']),
      data_type: 'number',
      options: null,
      is_required: true,
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      template_id: 1,
      step_number: 4,
      item_name: '网络设备状态检查',
      item_description: '检查路由器、交换机等网络设备运行状态',
      check_standard: '设备正常运行，指示灯状态正常，无告警',
      operation_guide: '1. 检查设备指示灯\n2. 查看设备显示屏\n3. 检查网络连接',
      validation_rules: JSON.stringify({
        required: true,
        type: 'select',
        options: ['正常', '异常', '需维护']
      }),
      require_photo: true,
      require_video: false,
      auto_capture: false,
      voice_prompts: JSON.stringify(['请检查网络设备', '请拍摄设备状态']),
      exception_keywords: JSON.stringify(['告警', '故障', '断网', '异常']),
      data_type: 'select',
      options: JSON.stringify(['正常', '异常', '需维护']),
      is_required: true,
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      template_id: 1,
      step_number: 5,
      item_name: '信号质量测试',
      item_description: '测试5G信号强度和质量',
      check_standard: 'RSRP > -100dBm, SINR > 10dB',
      operation_guide: '1. 使用测试设备\n2. 记录信号强度\n3. 记录信号质量',
      validation_rules: JSON.stringify({
        required: true,
        type: 'number',
        min: -120,
        max: -50
      }),
      require_photo: false,
      require_video: false,
      auto_capture: true,
      voice_prompts: JSON.stringify(['请测试信号质量', '请记录测试结果']),
      exception_keywords: JSON.stringify(['弱信号', '干扰', '质量差']),
      data_type: 'number',
      options: null,
      is_required: true,
      created_at: new Date(),
      updated_at: new Date()
    },

    // 4G基站模板检查项目
    {
      template_id: 2,
      step_number: 1,
      item_name: '基站设备检查',
      item_description: '检查4G基站主设备运行状态',
      check_standard: '设备正常运行，无告警，温度正常',
      operation_guide: '1. 检查设备指示灯\n2. 查看告警信息\n3. 检查设备温度',
      validation_rules: JSON.stringify({
        required: true,
        type: 'select',
        options: ['正常', '异常', '需维护']
      }),
      require_photo: true,
      require_video: false,
      auto_capture: false,
      voice_prompts: JSON.stringify(['请检查基站设备', '请拍摄设备照片']),
      exception_keywords: JSON.stringify(['告警', '故障', '过热']),
      data_type: 'select',
      options: JSON.stringify(['正常', '异常', '需维护']),
      is_required: true,
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      template_id: 2,
      step_number: 2,
      item_name: '传输链路检查',
      item_description: '检查传输设备和链路状态',
      check_standard: '传输正常，无丢包，延迟正常',
      operation_guide: '1. 检查传输设备\n2. 测试链路质量\n3. 记录测试结果',
      validation_rules: JSON.stringify({
        required: true,
        type: 'select',
        options: ['正常', '异常', '需维护']
      }),
      require_photo: false,
      require_video: false,
      auto_capture: true,
      voice_prompts: JSON.stringify(['请检查传输链路', '请记录测试结果']),
      exception_keywords: JSON.stringify(['丢包', '延迟', '中断']),
      data_type: 'select',
      options: JSON.stringify(['正常', '异常', '需维护']),
      is_required: true,
      created_at: new Date(),
      updated_at: new Date()
    },

    // 微基站模板检查项目
    {
      template_id: 3,
      step_number: 1,
      item_name: '设备外观检查',
      item_description: '检查微基站设备外观是否完好',
      check_standard: '设备外观完好，无破损，安装牢固',
      operation_guide: '1. 检查设备外观\n2. 检查安装情况\n3. 拍摄照片记录',
      validation_rules: JSON.stringify({
        required: true,
        type: 'select',
        options: ['正常', '异常', '需维护']
      }),
      require_photo: true,
      require_video: false,
      auto_capture: false,
      voice_prompts: JSON.stringify(['请检查设备外观', '请拍摄设备照片']),
      exception_keywords: JSON.stringify(['破损', '松动', '腐蚀']),
      data_type: 'select',
      options: JSON.stringify(['正常', '异常', '需维护']),
      is_required: true,
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      template_id: 3,
      step_number: 2,
      item_name: '覆盖测试',
      item_description: '测试微基站覆盖范围和信号质量',
      check_standard: '覆盖范围符合设计要求，信号质量良好',
      operation_guide: '1. 在覆盖区域测试\n2. 记录信号强度\n3. 检查覆盖盲区',
      validation_rules: JSON.stringify({
        required: true,
        type: 'text'
      }),
      require_photo: false,
      require_video: false,
      auto_capture: true,
      voice_prompts: JSON.stringify(['请进行覆盖测试', '请记录测试结果']),
      exception_keywords: JSON.stringify(['盲区', '弱覆盖', '干扰']),
      data_type: 'text',
      options: null,
      is_required: true,
      created_at: new Date(),
      updated_at: new Date()
    }
  ];

  await knex('inspection_items').insert(checkItems);
}
