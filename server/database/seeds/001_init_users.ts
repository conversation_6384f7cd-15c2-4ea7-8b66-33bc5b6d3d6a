import { Knex } from 'knex';
import bcrypt from 'bcryptjs';

export async function seed(knex: Knex): Promise<void> {
  // 清空现有数据
  await knex('users').del();

  // 创建默认密码哈希
  const defaultPasswordHash = await bcrypt.hash('admin123456', 12);

  // 插入种子数据
  await knex('users').insert([
    {
      employee_id: 'admin',
      name: '系统管理员',
      role: 'super_admin',
      department: '信息技术部',
      email: '<EMAIL>',
      phone: '13800138000',
      password_hash: defaultPasswordHash,
      status: 'active',
      created_at: new Date(),
      updated_at: new Date(),
    },
    {
      employee_id: '2001',
      name: '张三',
      role: 'site_admin',
      department: '运维部',
      email: 'zhang<PERSON>@example.com',
      phone: '13800138001',
      password_hash: defaultPasswordHash,
      status: 'active',
      created_at: new Date(),
      updated_at: new Date(),
    },
    {
      employee_id: '2002',
      name: '李四',
      role: 'inspector',
      department: '运维部',
      email: '<EMAIL>',
      phone: '13800138002',
      password_hash: defaultPasswordHash,
      status: 'active',
      created_at: new Date(),
      updated_at: new Date(),
    },
    {
      employee_id: '2003',
      name: '王五',
      role: 'inspector',
      department: '运维部',
      email: '<EMAIL>',
      phone: '13800138003',
      password_hash: defaultPasswordHash,
      status: 'active',
      created_at: new Date(),
      updated_at: new Date(),
    },
    {
      employee_id: '2004',
      name: '赵六',
      role: 'viewer',
      department: '质量部',
      email: '<EMAIL>',
      phone: '13800138004',
      password_hash: defaultPasswordHash,
      status: 'active',
      created_at: new Date(),
      updated_at: new Date(),
    },
  ]);

  console.log('用户种子数据插入完成');
  console.log('默认账户信息：');
  console.log('- 超级管理员: admin / admin123456');
  console.log('- 站点管理员: 2001 / admin123456');
  console.log('- 巡检员: 2002, 2003 / admin123456');
  console.log('- 查看者: 2004 / admin123456');
}
