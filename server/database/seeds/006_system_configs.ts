import { Knex } from 'knex';

export async function seed(knex: Knex): Promise<void> {
  // 清空现有数据
  await knex('system_configs').del();

  // 插入系统配置数据
  const configs = [
    // 基本设置
    {
      config_key: 'system.name',
      config_name: '系统名称',
      config_value: 'IoT巡检管理系统',
      default_value: 'IoT巡检管理系统',
      description: '系统显示名称',
      data_type: 'string',
      category: '基本设置',
      is_public: true,
      is_editable: true,
      validation_rules: JSON.stringify({
        required: true,
        maxLength: 100
      }),
      sort_order: 1,
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      config_key: 'system.version',
      config_name: '系统版本',
      config_value: 'v1.0.0',
      default_value: 'v1.0.0',
      description: '当前系统版本号',
      data_type: 'string',
      category: '基本设置',
      is_public: true,
      is_editable: false,
      validation_rules: JSON.stringify({
        required: true,
        pattern: '^v\\d+\\.\\d+\\.\\d+$'
      }),
      sort_order: 2,
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      config_key: 'system.description',
      config_name: '系统描述',
      config_value: '基于IoT技术的智能巡检管理系统，支持移动端巡检、数据分析、报表生成等功能',
      default_value: '基于IoT技术的智能巡检管理系统',
      description: '系统功能描述',
      data_type: 'text',
      category: '基本设置',
      is_public: true,
      is_editable: true,
      validation_rules: JSON.stringify({
        maxLength: 500
      }),
      sort_order: 3,
      created_at: new Date(),
      updated_at: new Date()
    },

    // 任务设置
    {
      config_key: 'task.auto_assign',
      config_name: '自动分配任务',
      config_value: 'true',
      default_value: 'false',
      description: '是否启用任务自动分配功能',
      data_type: 'boolean',
      category: '任务设置',
      is_public: false,
      is_editable: true,
      validation_rules: JSON.stringify({
        type: 'boolean'
      }),
      sort_order: 10,
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      config_key: 'task.default_duration',
      config_name: '默认任务时长',
      config_value: '120',
      default_value: '120',
      description: '新建任务的默认预计时长（分钟）',
      data_type: 'number',
      category: '任务设置',
      is_public: false,
      is_editable: true,
      validation_rules: JSON.stringify({
        type: 'number',
        min: 30,
        max: 480
      }),
      sort_order: 11,
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      config_key: 'task.overdue_threshold',
      config_name: '逾期提醒阈值',
      config_value: '24',
      default_value: '24',
      description: '任务逾期提醒时间阈值（小时）',
      data_type: 'number',
      category: '任务设置',
      is_public: false,
      is_editable: true,
      validation_rules: JSON.stringify({
        type: 'number',
        min: 1,
        max: 168
      }),
      sort_order: 12,
      created_at: new Date(),
      updated_at: new Date()
    },

    // 通知设置
    {
      config_key: 'notification.email_enabled',
      config_name: '邮件通知',
      config_value: 'true',
      default_value: 'false',
      description: '是否启用邮件通知功能',
      data_type: 'boolean',
      category: '通知设置',
      is_public: false,
      is_editable: true,
      validation_rules: JSON.stringify({
        type: 'boolean'
      }),
      sort_order: 20,
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      config_key: 'notification.sms_enabled',
      config_name: '短信通知',
      config_value: 'false',
      default_value: 'false',
      description: '是否启用短信通知功能',
      data_type: 'boolean',
      category: '通知设置',
      is_public: false,
      is_editable: true,
      validation_rules: JSON.stringify({
        type: 'boolean'
      }),
      sort_order: 21,
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      config_key: 'notification.email_server',
      config_name: '邮件服务器',
      config_value: 'smtp.company.com',
      default_value: '',
      description: 'SMTP邮件服务器地址',
      data_type: 'string',
      category: '通知设置',
      is_public: false,
      is_editable: true,
      validation_rules: JSON.stringify({
        pattern: '^[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$'
      }),
      sort_order: 22,
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      config_key: 'notification.email_port',
      config_name: '邮件端口',
      config_value: '587',
      default_value: '587',
      description: 'SMTP邮件服务器端口',
      data_type: 'number',
      category: '通知设置',
      is_public: false,
      is_editable: true,
      validation_rules: JSON.stringify({
        type: 'number',
        min: 1,
        max: 65535
      }),
      sort_order: 23,
      created_at: new Date(),
      updated_at: new Date()
    },

    // 安全设置
    {
      config_key: 'security.password_min_length',
      config_name: '密码最小长度',
      config_value: '8',
      default_value: '8',
      description: '用户密码最小长度要求',
      data_type: 'number',
      category: '安全设置',
      is_public: false,
      is_editable: true,
      validation_rules: JSON.stringify({
        type: 'number',
        min: 6,
        max: 20
      }),
      sort_order: 30,
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      config_key: 'security.password_complexity',
      config_name: '密码复杂度要求',
      config_value: 'true',
      default_value: 'true',
      description: '是否要求密码包含大小写字母、数字和特殊字符',
      data_type: 'boolean',
      category: '安全设置',
      is_public: false,
      is_editable: true,
      validation_rules: JSON.stringify({
        type: 'boolean'
      }),
      sort_order: 31,
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      config_key: 'security.login_max_attempts',
      config_name: '最大登录失败次数',
      config_value: '5',
      default_value: '5',
      description: '连续登录失败后锁定账户的最大次数',
      data_type: 'number',
      category: '安全设置',
      is_public: false,
      is_editable: true,
      validation_rules: JSON.stringify({
        type: 'number',
        min: 3,
        max: 10
      }),
      sort_order: 32,
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      config_key: 'security.lockout_duration',
      config_name: '账户锁定时间',
      config_value: '15',
      default_value: '15',
      description: '账户锁定时间（分钟）',
      data_type: 'number',
      category: '安全设置',
      is_public: false,
      is_editable: true,
      validation_rules: JSON.stringify({
        type: 'number',
        min: 5,
        max: 60
      }),
      sort_order: 33,
      created_at: new Date(),
      updated_at: new Date()
    },

    // 数据设置
    {
      config_key: 'data.backup_enabled',
      config_name: '自动备份',
      config_value: 'true',
      default_value: 'true',
      description: '是否启用数据自动备份功能',
      data_type: 'boolean',
      category: '数据设置',
      is_public: false,
      is_editable: true,
      validation_rules: JSON.stringify({
        type: 'boolean'
      }),
      sort_order: 40,
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      config_key: 'data.backup_interval',
      config_name: '备份间隔',
      config_value: '24',
      default_value: '24',
      description: '数据备份间隔时间（小时）',
      data_type: 'number',
      category: '数据设置',
      is_public: false,
      is_editable: true,
      validation_rules: JSON.stringify({
        type: 'number',
        min: 1,
        max: 168
      }),
      sort_order: 41,
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      config_key: 'data.retention_days',
      config_name: '数据保留天数',
      config_value: '365',
      default_value: '365',
      description: '历史数据保留天数',
      data_type: 'number',
      category: '数据设置',
      is_public: false,
      is_editable: true,
      validation_rules: JSON.stringify({
        type: 'number',
        min: 30,
        max: 3650
      }),
      sort_order: 42,
      created_at: new Date(),
      updated_at: new Date()
    }
  ];

  await knex('system_configs').insert(configs);
}
