import { Knex } from 'knex';

export async function seed(knex: Knex): Promise<void> {
  // 清空现有数据
  await knex('task_history').del();
  await knex('inspection_results').del();
  await knex('inspection_tasks').del();

  // 插入任务数据
  const tasks = [
    {
      id: 1,
      task_code: 'TASK-20240115-001',
      task_name: '北京朝阳5G基站月度巡检',
      description: '对北京朝阳5G基站进行月度例行巡检，检查设备运行状态和环境条件',
      template_id: 1, // 5G基站标准巡检模板
      site_id: 1, // 北京朝阳5G基站
      assigned_to: 2, // 假设用户ID 2 是李维护员
      status: 'in_progress',
      priority: 'high',
      scheduled_date: new Date('2024-01-15T09:00:00Z'),
      due_date: new Date('2024-01-15T17:00:00Z'),
      started_at: new Date('2024-01-15T09:15:00Z'),
      estimated_duration: 120,
      actual_duration: null,
      progress: 65,
      task_data: JSON.stringify({
        weather: '晴天',
        temperature: '15°C',
        special_notes: '设备运行正常'
      }),
      notes: '任务进行中，已完成电源和天线系统检查',
      created_by: 1,
      created_at: new Date('2024-01-10T10:00:00Z'),
      updated_at: new Date('2024-01-15T11:30:00Z')
    },
    {
      id: 2,
      task_code: 'TASK-20240116-001',
      task_name: '上海浦东5G基站紧急检修',
      description: '接到告警，需要对上海浦东5G基站进行紧急检修',
      template_id: 1, // 5G基站标准巡检模板
      site_id: 2, // 上海浦东5G基站
      assigned_to: 3, // 假设用户ID 3 是王技术员
      status: 'pending',
      priority: 'urgent',
      scheduled_date: new Date('2024-01-16T08:00:00Z'),
      due_date: new Date('2024-01-16T12:00:00Z'),
      started_at: null,
      estimated_duration: 90,
      actual_duration: null,
      progress: 0,
      task_data: JSON.stringify({
        alarm_type: 'UPS电源告警',
        alarm_level: '严重',
        reported_by: '监控中心'
      }),
      notes: '紧急任务，需要立即处理',
      created_by: 1,
      created_at: new Date('2024-01-15T14:30:00Z'),
      updated_at: new Date('2024-01-15T14:30:00Z')
    },
    {
      id: 3,
      task_code: 'TASK-20240112-001',
      task_name: '广州天河微基站周检',
      description: '广州天河微基站周度例行检查',
      template_id: 3, // 微基站快速巡检模板
      site_id: 3, // 广州天河微基站
      assigned_to: 4, // 假设用户ID 4 是陈维护员
      status: 'completed',
      priority: 'medium',
      scheduled_date: new Date('2024-01-12T10:00:00Z'),
      due_date: new Date('2024-01-12T12:00:00Z'),
      started_at: new Date('2024-01-12T10:05:00Z'),
      completed_at: new Date('2024-01-12T11:30:00Z'),
      estimated_duration: 45,
      actual_duration: 85,
      progress: 100,
      task_data: JSON.stringify({
        weather: '多云',
        temperature: '22°C',
        completion_notes: '所有检查项目正常'
      }),
      notes: '任务已完成，设备状态良好',
      created_by: 1,
      created_at: new Date('2024-01-08T09:00:00Z'),
      updated_at: new Date('2024-01-12T11:30:00Z')
    },
    {
      id: 4,
      task_code: 'TASK-20240114-001',
      task_name: '深圳南山5G基站季度检查',
      description: '深圳南山5G基站季度全面检查',
      template_id: 1, // 5G基站标准巡检模板
      site_id: 4, // 深圳南山5G基站
      assigned_to: 2, // 李维护员
      status: 'cancelled',
      priority: 'low',
      scheduled_date: new Date('2024-01-14T14:00:00Z'),
      due_date: new Date('2024-01-14T18:00:00Z'),
      started_at: null,
      estimated_duration: 120,
      actual_duration: null,
      progress: 0,
      task_data: JSON.stringify({
        cancellation_reason: '站址维护中，暂停巡检'
      }),
      notes: '由于站址正在维护，取消本次巡检',
      created_by: 1,
      created_at: new Date('2024-01-12T16:00:00Z'),
      updated_at: new Date('2024-01-14T08:00:00Z')
    },
    {
      id: 5,
      task_code: 'TASK-20240110-001',
      task_name: '成都锦江室分系统检查',
      description: '成都锦江室分系统月度检查',
      template_id: 2, // 4G基站常规巡检模板
      site_id: 5, // 成都锦江室分系统
      assigned_to: 3, // 王技术员
      status: 'overdue',
      priority: 'medium',
      scheduled_date: new Date('2024-01-10T09:00:00Z'),
      due_date: new Date('2024-01-10T15:00:00Z'),
      started_at: null,
      estimated_duration: 90,
      actual_duration: null,
      progress: 0,
      task_data: JSON.stringify({
        overdue_reason: '执行人请假，任务延期'
      }),
      notes: '任务已逾期，需要重新安排',
      created_by: 1,
      created_at: new Date('2024-01-08T10:00:00Z'),
      updated_at: new Date('2024-01-11T09:00:00Z')
    }
  ];

  await knex('inspection_tasks').insert(tasks);

  // 插入一些任务历史记录
  const taskHistory = [
    {
      task_id: 1,
      action: 'create',
      description: '任务创建',
      old_data: null,
      new_data: JSON.stringify({ status: 'pending' }),
      ip_address: '*************',
      user_agent: 'Mozilla/5.0',
      user_id: 1,
      created_at: new Date('2024-01-10T10:00:00Z'),
      updated_at: new Date('2024-01-10T10:00:00Z')
    },
    {
      task_id: 1,
      action: 'assign',
      description: '任务分配给李维护员',
      old_data: JSON.stringify({ assigned_to: null }),
      new_data: JSON.stringify({ assigned_to: 2 }),
      ip_address: '*************',
      user_agent: 'Mozilla/5.0',
      user_id: 1,
      created_at: new Date('2024-01-10T10:05:00Z'),
      updated_at: new Date('2024-01-10T10:05:00Z')
    },
    {
      task_id: 1,
      action: 'start',
      description: '任务开始执行',
      old_data: JSON.stringify({ status: 'pending' }),
      new_data: JSON.stringify({ status: 'in_progress' }),
      ip_address: '*************',
      user_agent: 'Mobile App',
      user_id: 2,
      created_at: new Date('2024-01-15T09:15:00Z'),
      updated_at: new Date('2024-01-15T09:15:00Z')
    },
    {
      task_id: 3,
      action: 'create',
      description: '任务创建',
      old_data: null,
      new_data: JSON.stringify({ status: 'pending' }),
      ip_address: '*************',
      user_agent: 'Mozilla/5.0',
      user_id: 1,
      created_at: new Date('2024-01-08T09:00:00Z'),
      updated_at: new Date('2024-01-08T09:00:00Z')
    },
    {
      task_id: 3,
      action: 'complete',
      description: '任务完成',
      old_data: JSON.stringify({ status: 'in_progress', progress: 80 }),
      new_data: JSON.stringify({ status: 'completed', progress: 100 }),
      ip_address: '192.168.1.300',
      user_agent: 'Mobile App',
      user_id: 4,
      created_at: new Date('2024-01-12T11:30:00Z'),
      updated_at: new Date('2024-01-12T11:30:00Z')
    }
  ];

  await knex('task_history').insert(taskHistory);

  // 插入一些检查结果数据（针对已完成的任务）
  const inspectionResults = [
    {
      task_id: 3,
      item_id: 9, // 微基站设备外观检查
      step_number: 1,
      status: 'completed',
      result_value: '正常',
      notes: '设备外观完好，无破损',
      photos: JSON.stringify(['photo1.jpg', 'photo2.jpg']),
      videos: null,
      voice_records: null,
      metadata: JSON.stringify({ check_duration: 300 }),
      is_exception: false,
      exception_reason: null,
      checked_at: new Date('2024-01-12T10:15:00Z'),
      checked_by: 4,
      created_at: new Date('2024-01-12T10:15:00Z'),
      updated_at: new Date('2024-01-12T10:15:00Z')
    },
    {
      task_id: 3,
      item_id: 10, // 微基站覆盖测试
      step_number: 2,
      status: 'completed',
      result_value: '覆盖范围正常，信号质量良好',
      notes: '测试点信号强度均在标准范围内',
      photos: null,
      videos: null,
      voice_records: null,
      metadata: JSON.stringify({ 
        signal_strength: -75,
        coverage_area: '100%',
        test_points: 5
      }),
      is_exception: false,
      exception_reason: null,
      checked_at: new Date('2024-01-12T11:00:00Z'),
      checked_by: 4,
      created_at: new Date('2024-01-12T11:00:00Z'),
      updated_at: new Date('2024-01-12T11:00:00Z')
    }
  ];

  await knex('inspection_results').insert(inspectionResults);
}
