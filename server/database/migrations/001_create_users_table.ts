import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema.createTable('users', (table) => {
    table.increments('id').primary();
    table.string('employee_id', 50).unique().notNullable().comment('工号');
    table.string('name', 100).notNullable().comment('姓名');
    table.enum('role', ['super_admin', 'site_admin', 'inspector', 'viewer'])
      .notNullable()
      .defaultTo('inspector')
      .comment('角色');
    table.string('department', 100).comment('部门');
    table.string('email', 255).comment('邮箱');
    table.string('phone', 20).comment('电话');
    table.string('password_hash', 255).notNullable().comment('密码哈希');
    table.enum('status', ['active', 'inactive', 'suspended'])
      .notNullable()
      .defaultTo('active')
      .comment('状态');
    table.timestamp('last_login_at').comment('最后登录时间');
    table.timestamps(true, true);
    
    // 索引
    table.index(['employee_id']);
    table.index(['role']);
    table.index(['status']);
    table.index(['department']);
  });
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('users');
}
