import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema.createTable('devices', (table) => {
    table.increments('id').primary();
    table.string('device_id', 100).unique().notNullable().comment('设备ID');
    table.string('device_name', 100).notNullable().comment('设备名称');
    table.string('device_type', 50).notNullable().defaultTo('smart_helmet').comment('设备类型');
    table.enum('status', ['online', 'offline', 'maintenance', 'error'])
      .notNullable()
      .defaultTo('offline')
      .comment('设备状态');
    table.integer('bound_user_id').unsigned().comment('绑定用户ID');
    table.string('firmware_version', 50).comment('固件版本');
    table.integer('battery_level').comment('电池电量(%)');
    table.json('device_config').comment('设备配置');
    table.timestamp('last_heartbeat').comment('最后心跳时间');
    table.timestamps(true, true);
    
    // 外键
    table.foreign('bound_user_id').references('id').inTable('users').onDelete('SET NULL');
    
    // 索引
    table.index(['device_id']);
    table.index(['status']);
    table.index(['bound_user_id']);
    table.index(['device_type']);
  });
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('devices');
}
