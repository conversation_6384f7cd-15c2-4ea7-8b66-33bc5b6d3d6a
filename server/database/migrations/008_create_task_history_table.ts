import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema.createTable('task_history', (table) => {
    table.increments('id').primary();
    table.integer('task_id').unsigned().notNullable().comment('任务ID');
    table.string('action', 50).notNullable().comment('操作类型');
    table.text('description').comment('操作描述');
    table.json('old_data').comment('变更前数据');
    table.json('new_data').comment('变更后数据');
    table.string('ip_address', 45).comment('IP地址');
    table.string('user_agent').comment('用户代理');
    table.integer('user_id').unsigned().notNullable().comment('操作用户ID');
    table.timestamps(true, true);
    
    // 外键
    table.foreign('task_id').references('id').inTable('inspection_tasks').onDelete('CASCADE');
    table.foreign('user_id').references('id').inTable('users');
    
    // 索引
    table.index(['task_id']);
    table.index(['action']);
    table.index(['user_id']);
    table.index(['created_at']);
  });
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('task_history');
}
