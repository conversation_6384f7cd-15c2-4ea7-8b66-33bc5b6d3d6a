import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema.createTable('inspection_tasks', (table) => {
    table.increments('id').primary();
    table.string('task_code', 50).unique().notNullable().comment('任务编号');
    table.string('task_name', 200).notNullable().comment('任务名称');
    table.text('description').comment('任务描述');
    table.integer('template_id').unsigned().notNullable().comment('模板ID');
    table.integer('site_id').unsigned().notNullable().comment('站址ID');
    table.integer('assigned_to').unsigned().comment('分配给用户ID');
    table.enum('status', ['pending', 'in_progress', 'completed', 'cancelled', 'overdue'])
      .notNullable()
      .defaultTo('pending')
      .comment('任务状态');
    table.enum('priority', ['low', 'medium', 'high', 'urgent'])
      .notNullable()
      .defaultTo('medium')
      .comment('优先级');
    table.datetime('scheduled_date').notNullable().comment('计划执行时间');
    table.datetime('due_date').notNullable().comment('截止时间');
    table.datetime('started_at').comment('开始时间');
    table.datetime('completed_at').comment('完成时间');
    table.integer('estimated_duration').comment('预计时长(分钟)');
    table.integer('actual_duration').comment('实际时长(分钟)');
    table.integer('progress').defaultTo(0).comment('进度百分比');
    table.json('task_data').comment('任务数据');
    table.text('notes').comment('备注');
    table.integer('created_by').unsigned().notNullable().comment('创建人');
    table.timestamps(true, true);
    
    // 外键
    table.foreign('template_id').references('id').inTable('inspection_templates');
    table.foreign('site_id').references('id').inTable('sites');
    table.foreign('assigned_to').references('id').inTable('users');
    table.foreign('created_by').references('id').inTable('users');
    
    // 索引
    table.index(['task_code']);
    table.index(['template_id']);
    table.index(['site_id']);
    table.index(['assigned_to']);
    table.index(['status']);
    table.index(['priority']);
    table.index(['scheduled_date']);
    table.index(['due_date']);
    table.index(['created_by']);
  });
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('inspection_tasks');
}
