import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema.createTable('inspection_templates', (table) => {
    table.increments('id').primary();
    table.string('template_name', 200).notNullable().comment('模板名称');
    table.string('template_code', 50).unique().notNullable().comment('模板编号');
    table.string('site_type', 50).notNullable().comment('适用站址类型');
    table.text('description').comment('描述');
    table.string('version', 20).notNullable().defaultTo('1.0').comment('版本号');
    table.enum('status', ['active', 'inactive', 'draft'])
      .notNullable()
      .defaultTo('draft')
      .comment('状态');
    table.integer('created_by').unsigned().notNullable().comment('创建人');
    table.timestamps(true, true);
    
    // 外键
    table.foreign('created_by').references('id').inTable('users');
    
    // 索引
    table.index(['template_code']);
    table.index(['site_type']);
    table.index(['status']);
    table.index(['created_by']);
  });
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('inspection_templates');
}
