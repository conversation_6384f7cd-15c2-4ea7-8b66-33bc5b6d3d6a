import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema.createTable('sites', (table) => {
    table.increments('id').primary();
    table.string('site_code', 50).unique().notNullable().comment('站址编号');
    table.string('site_name', 200).notNullable().comment('站址名称');
    table.text('address').notNullable().comment('地址');
    table.decimal('latitude', 10, 8).comment('纬度');
    table.decimal('longitude', 11, 8).comment('经度');
    table.string('site_type', 50).notNullable().comment('站址类型');
    table.string('site_level', 20).notNullable().comment('站址等级');
    table.text('description').comment('描述');
    table.json('equipment_list').comment('设备清单');
    table.json('technical_params').comment('技术参数');
    table.enum('status', ['active', 'inactive', 'maintenance'])
      .notNullable()
      .defaultTo('active')
      .comment('状态');
    table.timestamps(true, true);
    
    // 索引
    table.index(['site_code']);
    table.index(['site_type']);
    table.index(['site_level']);
    table.index(['status']);
    table.index(['latitude', 'longitude']);
  });
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('sites');
}
