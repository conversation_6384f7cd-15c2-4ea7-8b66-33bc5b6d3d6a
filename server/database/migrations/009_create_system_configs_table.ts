import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema.createTable('system_configs', (table) => {
    table.increments('id').primary();
    table.string('config_key', 100).unique().notNullable().comment('配置键');
    table.string('config_name', 200).notNullable().comment('配置名称');
    table.text('config_value').comment('配置值');
    table.text('default_value').comment('默认值');
    table.text('description').comment('配置描述');
    table.enum('data_type', ['string', 'number', 'boolean', 'json', 'text'])
      .notNullable()
      .defaultTo('string')
      .comment('数据类型');
    table.string('category', 50).notNullable().comment('配置分类');
    table.boolean('is_public').defaultTo(false).comment('是否公开');
    table.boolean('is_editable').defaultTo(true).comment('是否可编辑');
    table.json('validation_rules').comment('验证规则');
    table.integer('sort_order').defaultTo(0).comment('排序');
    table.timestamps(true, true);
    
    // 索引
    table.index(['config_key']);
    table.index(['category']);
    table.index(['is_public']);
    table.index(['sort_order']);
  });
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('system_configs');
}
