import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema.createTable('inspection_results', (table) => {
    table.increments('id').primary();
    table.integer('task_id').unsigned().notNullable().comment('任务ID');
    table.integer('item_id').unsigned().notNullable().comment('检查项目ID');
    table.integer('step_number').notNullable().comment('步骤序号');
    table.enum('status', ['pending', 'completed', 'failed', 'skipped'])
      .notNullable()
      .defaultTo('pending')
      .comment('检查状态');
    table.text('result_value').comment('检查结果值');
    table.text('notes').comment('备注');
    table.json('photos').comment('照片列表');
    table.json('videos').comment('视频列表');
    table.json('voice_records').comment('语音记录');
    table.json('metadata').comment('元数据');
    table.boolean('is_exception').defaultTo(false).comment('是否异常');
    table.text('exception_reason').comment('异常原因');
    table.datetime('checked_at').comment('检查时间');
    table.integer('checked_by').unsigned().comment('检查人');
    table.timestamps(true, true);
    
    // 外键
    table.foreign('task_id').references('id').inTable('inspection_tasks').onDelete('CASCADE');
    table.foreign('item_id').references('id').inTable('inspection_items');
    table.foreign('checked_by').references('id').inTable('users');
    
    // 索引
    table.index(['task_id']);
    table.index(['item_id']);
    table.index(['step_number']);
    table.index(['status']);
    table.index(['is_exception']);
    table.index(['checked_at']);
    table.index(['checked_by']);
    table.unique(['task_id', 'item_id']);
  });
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('inspection_results');
}
