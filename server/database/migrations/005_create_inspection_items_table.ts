import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema.createTable('inspection_items', (table) => {
    table.increments('id').primary();
    table.integer('template_id').unsigned().notNullable().comment('模板ID');
    table.integer('step_number').notNullable().comment('步骤序号');
    table.string('item_name', 200).notNullable().comment('检查项目名称');
    table.text('item_description').comment('检查项目描述');
    table.text('check_standard').comment('检查标准');
    table.text('operation_guide').comment('操作指引');
    table.json('validation_rules').comment('验证规则');
    table.boolean('require_photo').defaultTo(false).comment('是否需要拍照');
    table.boolean('require_video').defaultTo(false).comment('是否需要录像');
    table.boolean('auto_capture').defaultTo(false).comment('是否自动拍摄');
    table.json('voice_prompts').comment('语音提示');
    table.json('exception_keywords').comment('异常关键词');
    table.enum('data_type', ['text', 'number', 'boolean', 'select', 'multiselect'])
      .notNullable()
      .defaultTo('text')
      .comment('数据类型');
    table.json('options').comment('选项配置');
    table.boolean('is_required').defaultTo(true).comment('是否必填');
    table.timestamps(true, true);
    
    // 外键
    table.foreign('template_id').references('id').inTable('inspection_templates').onDelete('CASCADE');
    
    // 索引
    table.index(['template_id']);
    table.index(['step_number']);
    table.unique(['template_id', 'step_number']);
  });
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('inspection_items');
}
