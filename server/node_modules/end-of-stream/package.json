{"name": "end-of-stream", "version": "1.4.5", "description": "Call a callback when a readable/writable/duplex stream has completed or failed.", "repository": {"type": "git", "url": "git://github.com/mafintosh/end-of-stream.git"}, "dependencies": {"once": "^1.4.0"}, "scripts": {"test": "node test.js"}, "files": ["index.js"], "keywords": ["stream", "streams", "callback", "finish", "close", "end", "wait"], "bugs": {"url": "https://github.com/mafintosh/end-of-stream/issues"}, "homepage": "https://github.com/mafintosh/end-of-stream", "main": "index.js", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "devDependencies": {"tape": "^4.11.0"}}