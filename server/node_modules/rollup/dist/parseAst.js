/*
  @license
	Rollup.js v4.44.2
	Fri, 04 Jul 2025 12:55:10 GMT - commit d6dd1e7c6ee3f8fcfd77e5b8082cc62387a8ac4f

	https://github.com/rollup/rollup

	Released under the MIT License.
*/
'use strict';

Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });

require('./native.js');
const parseAst_js = require('./shared/parseAst.js');
require('node:path');



exports.parseAst = parseAst_js.parseAst;
exports.parseAstAsync = parseAst_js.parseAstAsync;
//# sourceMappingURL=parseAst.js.map
