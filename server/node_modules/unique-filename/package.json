{"name": "unique-filename", "version": "1.1.1", "description": "Generate a unique filename for use in temporary directories or caches.", "main": "index.js", "scripts": {"test": "standard && tap test"}, "repository": {"type": "git", "url": "https://github.com/iarna/unique-filename.git"}, "keywords": [], "author": "<PERSON> <<EMAIL>> (http://re-becca.org/)", "license": "ISC", "bugs": {"url": "https://github.com/iarna/unique-filename/issues"}, "homepage": "https://github.com/iarna/unique-filename", "devDependencies": {"standard": "^5.4.1", "tap": "^2.3.1"}, "dependencies": {"unique-slug": "^2.0.0"}}