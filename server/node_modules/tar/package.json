{"author": "GitHub Inc.", "name": "tar", "description": "tar for node", "version": "6.2.1", "repository": {"type": "git", "url": "https://github.com/isaacs/node-tar.git"}, "scripts": {"genparse": "node scripts/generate-parse-fixtures.js", "snap": "tap", "test": "tap"}, "dependencies": {"chownr": "^2.0.0", "fs-minipass": "^2.0.0", "minipass": "^5.0.0", "minizlib": "^2.1.1", "mkdirp": "^1.0.3", "yallist": "^4.0.0"}, "devDependencies": {"@npmcli/eslint-config": "^4.0.0", "@npmcli/template-oss": "4.11.0", "chmodr": "^1.2.0", "end-of-stream": "^1.4.3", "events-to-array": "^2.0.3", "mutate-fs": "^2.1.1", "nock": "^13.2.9", "rimraf": "^3.0.2", "tap": "^16.0.1"}, "license": "ISC", "engines": {"node": ">=10"}, "files": ["bin/", "lib/", "index.js"], "tap": {"coverage-map": "map.js", "timeout": 0, "nyc-arg": ["--exclude", "tap-snapshots/**"]}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.11.0", "content": "scripts/template-oss", "engines": ">=10", "distPaths": ["index.js"], "allowPaths": ["/index.js"], "ciVersions": ["10.x", "12.x", "14.x", "16.x", "18.x"]}}