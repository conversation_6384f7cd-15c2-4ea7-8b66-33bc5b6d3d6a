/*!
 * humanize-ms - index.js
 * Copyright(c) 2014 dead_horse <<EMAIL>>
 * MIT Licensed
 */

'use strict';

/**
 * Module dependencies.
 */

var util = require('util');
var ms = require('ms');

module.exports = function (t) {
  if (typeof t === 'number') return t;
  var r = ms(t);
  if (r === undefined) {
    var err = new Error(util.format('humanize-ms(%j) result undefined', t));
    console.warn(err.stack);
  }
  return r;
};
