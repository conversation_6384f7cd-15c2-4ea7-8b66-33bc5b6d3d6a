# Mosquitto MQTT Broker 配置文件

# 基本设置
pid_file /var/run/mosquitto.pid
persistence true
persistence_location /mosquitto/data/
log_dest file /mosquitto/log/mosquitto.log
log_dest stdout

# 网络设置
port 1883
max_inflight_messages 20
max_queued_messages 100
message_size_limit 0

# 安全设置
allow_anonymous true
# password_file /mosquitto/config/passwd
# acl_file /mosquitto/config/acl

# WebSocket 支持
listener 9883
protocol websockets

# 日志设置
log_type error
log_type warning
log_type notice
log_type information
log_type debug

# 连接设置
max_connections -1
keepalive_interval 60
retry_interval 20
sys_interval 10

# 持久化设置
autosave_interval 1800
autosave_on_changes false
persistent_client_expiration 2m
