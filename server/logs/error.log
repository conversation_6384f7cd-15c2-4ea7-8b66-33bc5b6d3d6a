2025-07-10 18:26:58 [ERROR]: 数据库初始化失败: password authentication failed for user "admin"
error: password authentication failed for user "admin"
    at Parser.parseErrorMessage (/Users/<USER>/Documents/workspace/dev/AISafeExplore/server/node_modules/pg-protocol/src/parser.ts:369:69)
    at Parser.handlePacket (/Users/<USER>/Documents/workspace/dev/AISafeExplore/server/node_modules/pg-protocol/src/parser.ts:187:21)
    at Parser.parse (/Users/<USER>/Documents/workspace/dev/AISafeExplore/server/node_modules/pg-protocol/src/parser.ts:102:30)
    at Socket.<anonymous> (/Users/<USER>/Documents/workspace/dev/AISafeExplore/server/node_modules/pg-protocol/src/index.ts:7:48)
    at Socket.emit (node:events:517:28)
    at Socket.emit (node:domain:489:12)
    at addChunk (node:internal/streams/readable:368:12)
    at readableAddChunk (node:internal/streams/readable:341:9)
    at Socket.Readable.push (node:internal/streams/readable:278:10)
    at TCP.onStreamRead (node:internal/stream_base_commons:190:23)
{
  "service": "inspection-system",
  "length": 101,
  "name": "error",
  "severity": "FATAL",
  "code": "28P01",
  "file": "auth.c",
  "line": "324",
  "routine": "auth_failed"
}
2025-07-10 18:28:38 [ERROR]: 数据库初始化失败: ENOENT: no such file or directory, scandir '/Users/<USER>/Documents/workspace/dev/AISafeExplore/database/migrations'
Error: ENOENT: no such file or directory, scandir '/Users/<USER>/Documents/workspace/dev/AISafeExplore/database/migrations'
{
  "service": "inspection-system",
  "errno": -2,
  "code": "ENOENT",
  "syscall": "scandir",
  "path": "/Users/<USER>/Documents/workspace/dev/AISafeExplore/database/migrations"
}
