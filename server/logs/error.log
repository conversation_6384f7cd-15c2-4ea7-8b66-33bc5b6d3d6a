2025-07-10 18:26:58 [ERROR]: 数据库初始化失败: password authentication failed for user "admin"
error: password authentication failed for user "admin"
    at Parser.parseErrorMessage (/Users/<USER>/Documents/workspace/dev/AISafeExplore/server/node_modules/pg-protocol/src/parser.ts:369:69)
    at Parser.handlePacket (/Users/<USER>/Documents/workspace/dev/AISafeExplore/server/node_modules/pg-protocol/src/parser.ts:187:21)
    at Parser.parse (/Users/<USER>/Documents/workspace/dev/AISafeExplore/server/node_modules/pg-protocol/src/parser.ts:102:30)
    at Socket.<anonymous> (/Users/<USER>/Documents/workspace/dev/AISafeExplore/server/node_modules/pg-protocol/src/index.ts:7:48)
    at Socket.emit (node:events:517:28)
    at Socket.emit (node:domain:489:12)
    at addChunk (node:internal/streams/readable:368:12)
    at readableAddChunk (node:internal/streams/readable:341:9)
    at Socket.Readable.push (node:internal/streams/readable:278:10)
    at TCP.onStreamRead (node:internal/stream_base_commons:190:23)
{
  "service": "inspection-system",
  "length": 101,
  "name": "error",
  "severity": "FATAL",
  "code": "28P01",
  "file": "auth.c",
  "line": "324",
  "routine": "auth_failed"
}
2025-07-10 18:28:38 [ERROR]: 数据库初始化失败: ENOENT: no such file or directory, scandir '/Users/<USER>/Documents/workspace/dev/AISafeExplore/database/migrations'
Error: ENOENT: no such file or directory, scandir '/Users/<USER>/Documents/workspace/dev/AISafeExplore/database/migrations'
{
  "service": "inspection-system",
  "errno": -2,
  "code": "ENOENT",
  "syscall": "scandir",
  "path": "/Users/<USER>/Documents/workspace/dev/AISafeExplore/database/migrations"
}
2025-07-11 10:53:54 [ERROR]: 密码验证失败: Illegal arguments: string, undefined
Error: Illegal arguments: string, undefined
    at _async (/Users/<USER>/Documents/workspace/dev/AISafeExplore/server/node_modules/bcryptjs/dist/bcrypt.js:286:46)
    at /Users/<USER>/Documents/workspace/dev/AISafeExplore/server/node_modules/bcryptjs/dist/bcrypt.js:307:17
    at new Promise (<anonymous>)
    at Object.bcrypt.compare (/Users/<USER>/Documents/workspace/dev/AISafeExplore/server/node_modules/bcryptjs/dist/bcrypt.js:306:20)
    at verifyPassword (/Users/<USER>/Documents/workspace/dev/AISafeExplore/server/src/middleware/simple-auth.ts:34:25)
    at /Users/<USER>/Documents/workspace/dev/AISafeExplore/server/src/routes/simple-auth.ts:66:47
{
  "service": "inspection-system"
}
2025-07-11 10:53:54 [ERROR]: 服务器错误:
UnauthorizedError: 密码错误
    at /Users/<USER>/Documents/workspace/dev/AISafeExplore/server/src/routes/simple-auth.ts:68:11
{
  "service": "inspection-system",
  "error": "密码错误",
  "url": "/api/auth/login",
  "method": "POST",
  "ip": "::1",
  "userAgent": "curl/8.7.1"
}
2025-07-11 10:55:14 [ERROR]: 密码验证失败: Illegal arguments: string, undefined
Error: Illegal arguments: string, undefined
    at _async (/Users/<USER>/Documents/workspace/dev/AISafeExplore/server/node_modules/bcryptjs/dist/bcrypt.js:286:46)
    at /Users/<USER>/Documents/workspace/dev/AISafeExplore/server/node_modules/bcryptjs/dist/bcrypt.js:307:17
    at new Promise (<anonymous>)
    at Object.bcrypt.compare (/Users/<USER>/Documents/workspace/dev/AISafeExplore/server/node_modules/bcryptjs/dist/bcrypt.js:306:20)
    at verifyPassword (/Users/<USER>/Documents/workspace/dev/AISafeExplore/server/src/middleware/simple-auth.ts:34:25)
    at /Users/<USER>/Documents/workspace/dev/AISafeExplore/server/src/routes/simple-auth.ts:66:47
{
  "service": "inspection-system"
}
2025-07-11 10:55:14 [ERROR]: 服务器错误:
UnauthorizedError: 密码错误
    at /Users/<USER>/Documents/workspace/dev/AISafeExplore/server/src/routes/simple-auth.ts:68:11
{
  "service": "inspection-system",
  "error": "密码错误",
  "url": "/api/auth/login",
  "method": "POST",
  "ip": "::1",
  "userAgent": "curl/8.7.1"
}
2025-07-11 10:56:25 [ERROR]: 密码验证失败: Illegal arguments: string, undefined
Error: Illegal arguments: string, undefined
    at _async (/Users/<USER>/Documents/workspace/dev/AISafeExplore/server/node_modules/bcryptjs/dist/bcrypt.js:286:46)
    at /Users/<USER>/Documents/workspace/dev/AISafeExplore/server/node_modules/bcryptjs/dist/bcrypt.js:307:17
    at new Promise (<anonymous>)
    at Object.bcrypt.compare (/Users/<USER>/Documents/workspace/dev/AISafeExplore/server/node_modules/bcryptjs/dist/bcrypt.js:306:20)
    at verifyPassword (/Users/<USER>/Documents/workspace/dev/AISafeExplore/server/src/middleware/simple-auth.ts:34:25)
    at /Users/<USER>/Documents/workspace/dev/AISafeExplore/server/src/routes/simple-auth.ts:66:47
{
  "service": "inspection-system"
}
2025-07-11 10:56:25 [ERROR]: 服务器错误:
UnauthorizedError: 密码错误
    at /Users/<USER>/Documents/workspace/dev/AISafeExplore/server/src/routes/simple-auth.ts:68:11
{
  "service": "inspection-system",
  "error": "密码错误",
  "url": "/api/auth/login",
  "method": "POST",
  "ip": "::1",
  "userAgent": "curl/8.7.1"
}
2025-07-11 10:57:10 [ERROR]: 密码验证失败: Illegal arguments: string, undefined
Error: Illegal arguments: string, undefined
    at _async (/Users/<USER>/Documents/workspace/dev/AISafeExplore/server/node_modules/bcryptjs/dist/bcrypt.js:286:46)
    at /Users/<USER>/Documents/workspace/dev/AISafeExplore/server/node_modules/bcryptjs/dist/bcrypt.js:307:17
    at new Promise (<anonymous>)
    at Object.bcrypt.compare (/Users/<USER>/Documents/workspace/dev/AISafeExplore/server/node_modules/bcryptjs/dist/bcrypt.js:306:20)
    at verifyPassword (/Users/<USER>/Documents/workspace/dev/AISafeExplore/server/src/middleware/simple-auth.ts:34:25)
    at /Users/<USER>/Documents/workspace/dev/AISafeExplore/server/src/routes/simple-auth.ts:66:47
{
  "service": "inspection-system"
}
2025-07-11 10:57:10 [ERROR]: 服务器错误:
UnauthorizedError: 密码错误
    at /Users/<USER>/Documents/workspace/dev/AISafeExplore/server/src/routes/simple-auth.ts:68:11
{
  "service": "inspection-system",
  "error": "密码错误",
  "url": "/api/auth/login",
  "method": "POST",
  "ip": "::1",
  "userAgent": "curl/8.7.1"
}
2025-07-11 10:58:14 [ERROR]: 密码验证失败: Illegal arguments: string, undefined
Error: Illegal arguments: string, undefined
    at _async (/Users/<USER>/Documents/workspace/dev/AISafeExplore/server/node_modules/bcryptjs/dist/bcrypt.js:286:46)
    at /Users/<USER>/Documents/workspace/dev/AISafeExplore/server/node_modules/bcryptjs/dist/bcrypt.js:307:17
    at new Promise (<anonymous>)
    at Object.bcrypt.compare (/Users/<USER>/Documents/workspace/dev/AISafeExplore/server/node_modules/bcryptjs/dist/bcrypt.js:306:20)
    at verifyPassword (/Users/<USER>/Documents/workspace/dev/AISafeExplore/server/src/middleware/simple-auth.ts:34:25)
    at /Users/<USER>/Documents/workspace/dev/AISafeExplore/server/src/routes/simple-auth.ts:66:47
{
  "service": "inspection-system"
}
2025-07-11 10:58:14 [ERROR]: 服务器错误:
UnauthorizedError: 密码错误
    at /Users/<USER>/Documents/workspace/dev/AISafeExplore/server/src/routes/simple-auth.ts:68:11
{
  "service": "inspection-system",
  "error": "密码错误",
  "url": "/api/auth/login",
  "method": "POST",
  "ip": "::1",
  "userAgent": "curl/8.7.1"
}
