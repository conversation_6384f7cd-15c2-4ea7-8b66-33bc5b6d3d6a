2025-07-10 18:26:58 [ERROR]: 数据库初始化失败: password authentication failed for user "admin"
error: password authentication failed for user "admin"
    at Parser.parseErrorMessage (/Users/<USER>/Documents/workspace/dev/AISafeExplore/server/node_modules/pg-protocol/src/parser.ts:369:69)
    at Parser.handlePacket (/Users/<USER>/Documents/workspace/dev/AISafeExplore/server/node_modules/pg-protocol/src/parser.ts:187:21)
    at Parser.parse (/Users/<USER>/Documents/workspace/dev/AISafeExplore/server/node_modules/pg-protocol/src/parser.ts:102:30)
    at Socket.<anonymous> (/Users/<USER>/Documents/workspace/dev/AISafeExplore/server/node_modules/pg-protocol/src/index.ts:7:48)
    at Socket.emit (node:events:517:28)
    at Socket.emit (node:domain:489:12)
    at addChunk (node:internal/streams/readable:368:12)
    at readableAddChunk (node:internal/streams/readable:341:9)
    at Socket.Readable.push (node:internal/streams/readable:278:10)
    at TCP.onStreamRead (node:internal/stream_base_commons:190:23)
{
  "service": "inspection-system",
  "length": 101,
  "name": "error",
  "severity": "FATAL",
  "code": "28P01",
  "file": "auth.c",
  "line": "324",
  "routine": "auth_failed"
}
2025-07-10 18:28:38 [INFO]: 数据库连接成功
{
  "service": "inspection-system"
}
2025-07-10 18:28:38 [ERROR]: 数据库初始化失败: ENOENT: no such file or directory, scandir '/Users/<USER>/Documents/workspace/dev/AISafeExplore/database/migrations'
Error: ENOENT: no such file or directory, scandir '/Users/<USER>/Documents/workspace/dev/AISafeExplore/database/migrations'
{
  "service": "inspection-system",
  "errno": -2,
  "code": "ENOENT",
  "syscall": "scandir",
  "path": "/Users/<USER>/Documents/workspace/dev/AISafeExplore/database/migrations"
}
2025-07-10 18:33:53 [INFO]: 数据库连接成功
{
  "service": "inspection-system"
}
2025-07-10 18:33:53 [INFO]: 数据库已是最新版本，无需迁移
{
  "service": "inspection-system"
}
2025-07-10 18:33:53 [INFO]: 数据库初始化完成
{
  "service": "inspection-system"
}
2025-07-10 18:33:53 [INFO]: 服务器启动成功: http://localhost:3000
{
  "service": "inspection-system"
}
2025-07-10 18:33:53 [INFO]: 环境: development
{
  "service": "inspection-system"
}
2025-07-10 18:34:50 [INFO]: ::1 - - [10/Jul/2025:10:34:50 +0000] "GET /health HTTP/1.1" 200 103 "-" "curl/8.7.1"
{
  "service": "inspection-system"
}
2025-07-10 18:36:40 [INFO]: ::1 - - [10/Jul/2025:10:36:40 +0000] "POST /api/auth/login HTTP/1.1" 404 121 "-" "curl/8.7.1"
{
  "service": "inspection-system"
}
