2025-07-10 18:26:58 [ERROR]: 数据库初始化失败: password authentication failed for user "admin"
error: password authentication failed for user "admin"
    at Parser.parseErrorMessage (/Users/<USER>/Documents/workspace/dev/AISafeExplore/server/node_modules/pg-protocol/src/parser.ts:369:69)
    at Parser.handlePacket (/Users/<USER>/Documents/workspace/dev/AISafeExplore/server/node_modules/pg-protocol/src/parser.ts:187:21)
    at Parser.parse (/Users/<USER>/Documents/workspace/dev/AISafeExplore/server/node_modules/pg-protocol/src/parser.ts:102:30)
    at Socket.<anonymous> (/Users/<USER>/Documents/workspace/dev/AISafeExplore/server/node_modules/pg-protocol/src/index.ts:7:48)
    at Socket.emit (node:events:517:28)
    at Socket.emit (node:domain:489:12)
    at addChunk (node:internal/streams/readable:368:12)
    at readableAddChunk (node:internal/streams/readable:341:9)
    at Socket.Readable.push (node:internal/streams/readable:278:10)
    at TCP.onStreamRead (node:internal/stream_base_commons:190:23)
{
  "service": "inspection-system",
  "length": 101,
  "name": "error",
  "severity": "FATAL",
  "code": "28P01",
  "file": "auth.c",
  "line": "324",
  "routine": "auth_failed"
}
2025-07-10 18:28:38 [INFO]: 数据库连接成功
{
  "service": "inspection-system"
}
2025-07-10 18:28:38 [ERROR]: 数据库初始化失败: ENOENT: no such file or directory, scandir '/Users/<USER>/Documents/workspace/dev/AISafeExplore/database/migrations'
Error: ENOENT: no such file or directory, scandir '/Users/<USER>/Documents/workspace/dev/AISafeExplore/database/migrations'
{
  "service": "inspection-system",
  "errno": -2,
  "code": "ENOENT",
  "syscall": "scandir",
  "path": "/Users/<USER>/Documents/workspace/dev/AISafeExplore/database/migrations"
}
2025-07-10 18:33:53 [INFO]: 数据库连接成功
{
  "service": "inspection-system"
}
2025-07-10 18:33:53 [INFO]: 数据库已是最新版本，无需迁移
{
  "service": "inspection-system"
}
2025-07-10 18:33:53 [INFO]: 数据库初始化完成
{
  "service": "inspection-system"
}
2025-07-10 18:33:53 [INFO]: 服务器启动成功: http://localhost:3000
{
  "service": "inspection-system"
}
2025-07-10 18:33:53 [INFO]: 环境: development
{
  "service": "inspection-system"
}
2025-07-10 18:34:50 [INFO]: ::1 - - [10/Jul/2025:10:34:50 +0000] "GET /health HTTP/1.1" 200 103 "-" "curl/8.7.1"
{
  "service": "inspection-system"
}
2025-07-10 18:36:40 [INFO]: ::1 - - [10/Jul/2025:10:36:40 +0000] "POST /api/auth/login HTTP/1.1" 404 121 "-" "curl/8.7.1"
{
  "service": "inspection-system"
}
2025-07-11 10:53:25 [INFO]: 数据库连接成功
{
  "service": "inspection-system"
}
2025-07-11 10:53:25 [INFO]: 数据库已是最新版本，无需迁移
{
  "service": "inspection-system"
}
2025-07-11 10:53:25 [INFO]: 数据库初始化完成
{
  "service": "inspection-system"
}
2025-07-11 10:53:25 [INFO]: 服务器启动成功: http://localhost:3000
{
  "service": "inspection-system"
}
2025-07-11 10:53:25 [INFO]: 环境: development
{
  "service": "inspection-system"
}
2025-07-11 10:53:54 [ERROR]: 密码验证失败: Illegal arguments: string, undefined
Error: Illegal arguments: string, undefined
    at _async (/Users/<USER>/Documents/workspace/dev/AISafeExplore/server/node_modules/bcryptjs/dist/bcrypt.js:286:46)
    at /Users/<USER>/Documents/workspace/dev/AISafeExplore/server/node_modules/bcryptjs/dist/bcrypt.js:307:17
    at new Promise (<anonymous>)
    at Object.bcrypt.compare (/Users/<USER>/Documents/workspace/dev/AISafeExplore/server/node_modules/bcryptjs/dist/bcrypt.js:306:20)
    at verifyPassword (/Users/<USER>/Documents/workspace/dev/AISafeExplore/server/src/middleware/simple-auth.ts:34:25)
    at /Users/<USER>/Documents/workspace/dev/AISafeExplore/server/src/routes/simple-auth.ts:66:47
{
  "service": "inspection-system"
}
2025-07-11 10:53:54 [ERROR]: 服务器错误:
UnauthorizedError: 密码错误
    at /Users/<USER>/Documents/workspace/dev/AISafeExplore/server/src/routes/simple-auth.ts:68:11
{
  "service": "inspection-system",
  "error": "密码错误",
  "url": "/api/auth/login",
  "method": "POST",
  "ip": "::1",
  "userAgent": "curl/8.7.1"
}
2025-07-11 10:53:54 [INFO]: ::1 - - [11/Jul/2025:02:53:54 +0000] "POST /api/auth/login HTTP/1.1" 500 306 "-" "curl/8.7.1"
{
  "service": "inspection-system"
}
2025-07-11 10:55:14 [ERROR]: 密码验证失败: Illegal arguments: string, undefined
Error: Illegal arguments: string, undefined
    at _async (/Users/<USER>/Documents/workspace/dev/AISafeExplore/server/node_modules/bcryptjs/dist/bcrypt.js:286:46)
    at /Users/<USER>/Documents/workspace/dev/AISafeExplore/server/node_modules/bcryptjs/dist/bcrypt.js:307:17
    at new Promise (<anonymous>)
    at Object.bcrypt.compare (/Users/<USER>/Documents/workspace/dev/AISafeExplore/server/node_modules/bcryptjs/dist/bcrypt.js:306:20)
    at verifyPassword (/Users/<USER>/Documents/workspace/dev/AISafeExplore/server/src/middleware/simple-auth.ts:34:25)
    at /Users/<USER>/Documents/workspace/dev/AISafeExplore/server/src/routes/simple-auth.ts:66:47
{
  "service": "inspection-system"
}
2025-07-11 10:55:14 [ERROR]: 服务器错误:
UnauthorizedError: 密码错误
    at /Users/<USER>/Documents/workspace/dev/AISafeExplore/server/src/routes/simple-auth.ts:68:11
{
  "service": "inspection-system",
  "error": "密码错误",
  "url": "/api/auth/login",
  "method": "POST",
  "ip": "::1",
  "userAgent": "curl/8.7.1"
}
2025-07-11 10:55:14 [INFO]: ::1 - - [11/Jul/2025:02:55:14 +0000] "POST /api/auth/login HTTP/1.1" 500 306 "-" "curl/8.7.1"
{
  "service": "inspection-system"
}
2025-07-11 10:56:25 [ERROR]: 密码验证失败: Illegal arguments: string, undefined
Error: Illegal arguments: string, undefined
    at _async (/Users/<USER>/Documents/workspace/dev/AISafeExplore/server/node_modules/bcryptjs/dist/bcrypt.js:286:46)
    at /Users/<USER>/Documents/workspace/dev/AISafeExplore/server/node_modules/bcryptjs/dist/bcrypt.js:307:17
    at new Promise (<anonymous>)
    at Object.bcrypt.compare (/Users/<USER>/Documents/workspace/dev/AISafeExplore/server/node_modules/bcryptjs/dist/bcrypt.js:306:20)
    at verifyPassword (/Users/<USER>/Documents/workspace/dev/AISafeExplore/server/src/middleware/simple-auth.ts:34:25)
    at /Users/<USER>/Documents/workspace/dev/AISafeExplore/server/src/routes/simple-auth.ts:66:47
{
  "service": "inspection-system"
}
2025-07-11 10:56:25 [ERROR]: 服务器错误:
UnauthorizedError: 密码错误
    at /Users/<USER>/Documents/workspace/dev/AISafeExplore/server/src/routes/simple-auth.ts:68:11
{
  "service": "inspection-system",
  "error": "密码错误",
  "url": "/api/auth/login",
  "method": "POST",
  "ip": "::1",
  "userAgent": "curl/8.7.1"
}
2025-07-11 10:56:25 [INFO]: ::1 - - [11/Jul/2025:02:56:25 +0000] "POST /api/auth/login HTTP/1.1" 500 306 "-" "curl/8.7.1"
{
  "service": "inspection-system"
}
2025-07-11 10:57:10 [ERROR]: 密码验证失败: Illegal arguments: string, undefined
Error: Illegal arguments: string, undefined
    at _async (/Users/<USER>/Documents/workspace/dev/AISafeExplore/server/node_modules/bcryptjs/dist/bcrypt.js:286:46)
    at /Users/<USER>/Documents/workspace/dev/AISafeExplore/server/node_modules/bcryptjs/dist/bcrypt.js:307:17
    at new Promise (<anonymous>)
    at Object.bcrypt.compare (/Users/<USER>/Documents/workspace/dev/AISafeExplore/server/node_modules/bcryptjs/dist/bcrypt.js:306:20)
    at verifyPassword (/Users/<USER>/Documents/workspace/dev/AISafeExplore/server/src/middleware/simple-auth.ts:34:25)
    at /Users/<USER>/Documents/workspace/dev/AISafeExplore/server/src/routes/simple-auth.ts:66:47
{
  "service": "inspection-system"
}
2025-07-11 10:57:10 [ERROR]: 服务器错误:
UnauthorizedError: 密码错误
    at /Users/<USER>/Documents/workspace/dev/AISafeExplore/server/src/routes/simple-auth.ts:68:11
{
  "service": "inspection-system",
  "error": "密码错误",
  "url": "/api/auth/login",
  "method": "POST",
  "ip": "::1",
  "userAgent": "curl/8.7.1"
}
2025-07-11 10:57:10 [INFO]: ::1 - - [11/Jul/2025:02:57:10 +0000] "POST /api/auth/login HTTP/1.1" 500 306 "-" "curl/8.7.1"
{
  "service": "inspection-system"
}
2025-07-11 10:58:14 [ERROR]: 密码验证失败: Illegal arguments: string, undefined
Error: Illegal arguments: string, undefined
    at _async (/Users/<USER>/Documents/workspace/dev/AISafeExplore/server/node_modules/bcryptjs/dist/bcrypt.js:286:46)
    at /Users/<USER>/Documents/workspace/dev/AISafeExplore/server/node_modules/bcryptjs/dist/bcrypt.js:307:17
    at new Promise (<anonymous>)
    at Object.bcrypt.compare (/Users/<USER>/Documents/workspace/dev/AISafeExplore/server/node_modules/bcryptjs/dist/bcrypt.js:306:20)
    at verifyPassword (/Users/<USER>/Documents/workspace/dev/AISafeExplore/server/src/middleware/simple-auth.ts:34:25)
    at /Users/<USER>/Documents/workspace/dev/AISafeExplore/server/src/routes/simple-auth.ts:66:47
{
  "service": "inspection-system"
}
2025-07-11 10:58:14 [ERROR]: 服务器错误:
UnauthorizedError: 密码错误
    at /Users/<USER>/Documents/workspace/dev/AISafeExplore/server/src/routes/simple-auth.ts:68:11
{
  "service": "inspection-system",
  "error": "密码错误",
  "url": "/api/auth/login",
  "method": "POST",
  "ip": "::1",
  "userAgent": "curl/8.7.1"
}
2025-07-11 10:58:14 [INFO]: ::1 - - [11/Jul/2025:02:58:14 +0000] "POST /api/auth/login HTTP/1.1" 500 306 "-" "curl/8.7.1"
{
  "service": "inspection-system"
}
2025-07-11 10:58:57 [INFO]: 数据库连接成功
{
  "service": "inspection-system"
}
2025-07-11 10:58:57 [INFO]: 数据库已是最新版本，无需迁移
{
  "service": "inspection-system"
}
2025-07-11 10:58:57 [INFO]: 数据库初始化完成
{
  "service": "inspection-system"
}
2025-07-11 10:58:57 [INFO]: 服务器启动成功: http://localhost:3000
{
  "service": "inspection-system"
}
2025-07-11 10:58:57 [INFO]: 环境: development
{
  "service": "inspection-system"
}
2025-07-11 10:59:22 [INFO]: 查找用户结果:
{
  "service": "inspection-system",
  "employee_id": "admin",
  "found": true,
  "userKeys": [
    "id",
    "employee_id",
    "name",
    "role",
    "department",
    "email",
    "phone",
    "password_hash",
    "status",
    "last_login_at",
    "created_at",
    "updated_at"
  ],
  "hasPasswordHash": true
}
2025-07-11 10:59:22 [INFO]: 验证密码:
{
  "service": "inspection-system",
  "employee_id": "admin",
  "hasPassword": true,
  "hasHash": true,
  "hashLength": 60
}
2025-07-11 10:59:22 [INFO]: 密码验证结果:
{
  "service": "inspection-system",
  "isPasswordValid": true
}
2025-07-11 10:59:22 [INFO]: 用户登录成功:
{
  "service": "inspection-system",
  "userId": 1,
  "employeeId": "admin",
  "name": "系统管理员",
  "ip": "::1"
}
2025-07-11 10:59:22 [INFO]: ::1 - - [11/Jul/2025:02:59:22 +0000] "POST /api/auth/login HTTP/1.1" 200 920 "-" "curl/8.7.1"
{
  "service": "inspection-system"
}
2025-07-11 10:59:35 [INFO]: ::1 - - [11/Jul/2025:02:59:35 +0000] "GET /api/auth/me HTTP/1.1" 200 368 "-" "curl/8.7.1"
{
  "service": "inspection-system"
}
2025-07-11 11:01:19 [INFO]: ::1 - - [11/Jul/2025:03:01:19 +0000] "GET /api/sites HTTP/1.1" 404 116 "-" "curl/8.7.1"
{
  "service": "inspection-system"
}
2025-07-11 11:01:39 [INFO]: 数据库连接成功
{
  "service": "inspection-system"
}
2025-07-11 11:01:39 [INFO]: 数据库已是最新版本，无需迁移
{
  "service": "inspection-system"
}
2025-07-11 11:01:39 [INFO]: 数据库初始化完成
{
  "service": "inspection-system"
}
2025-07-11 11:01:39 [INFO]: 服务器启动成功: http://localhost:3000
{
  "service": "inspection-system"
}
2025-07-11 11:01:39 [INFO]: 环境: development
{
  "service": "inspection-system"
}
2025-07-11 11:02:22 [INFO]: ::1 - - [11/Jul/2025:03:02:22 +0000] "GET /api/sites HTTP/1.1" 200 6907 "-" "curl/8.7.1"
{
  "service": "inspection-system"
}
2025-07-11 11:02:51 [INFO]: ::1 - - [11/Jul/2025:03:02:51 +0000] "GET /api/sites/statistics/overview HTTP/1.1" 200 500 "-" "curl/8.7.1"
{
  "service": "inspection-system"
}
2025-07-11 12:09:02 [INFO]: 查找用户结果:
{
  "service": "inspection-system",
  "employee_id": "admin",
  "found": true,
  "userKeys": [
    "id",
    "employee_id",
    "name",
    "role",
    "department",
    "email",
    "phone",
    "password_hash",
    "status",
    "last_login_at",
    "created_at",
    "updated_at"
  ],
  "hasPasswordHash": true
}
2025-07-11 12:09:02 [INFO]: 验证密码:
{
  "service": "inspection-system",
  "employee_id": "admin",
  "hasPassword": true,
  "hasHash": true,
  "hashLength": 60
}
2025-07-11 12:09:03 [INFO]: 密码验证结果:
{
  "service": "inspection-system",
  "isPasswordValid": true
}
2025-07-11 12:09:03 [INFO]: 用户登录成功:
{
  "service": "inspection-system",
  "userId": 1,
  "employeeId": "admin",
  "name": "系统管理员",
  "ip": "::1"
}
2025-07-11 12:09:03 [INFO]: ::1 - - [11/Jul/2025:04:09:03 +0000] "POST /api/auth/login HTTP/1.1" 200 929 "-" "curl/8.7.1"
{
  "service": "inspection-system"
}
2025-07-11 12:09:15 [INFO]: ::1 - - [11/Jul/2025:04:09:15 +0000] "GET /api/templates HTTP/1.1" 404 120 "-" "curl/8.7.1"
{
  "service": "inspection-system"
}
2025-07-11 12:09:51 [INFO]: 数据库连接成功
{
  "service": "inspection-system"
}
2025-07-11 12:09:51 [INFO]: 数据库已是最新版本，无需迁移
{
  "service": "inspection-system"
}
2025-07-11 12:09:51 [INFO]: 数据库初始化完成
{
  "service": "inspection-system"
}
2025-07-11 12:09:51 [INFO]: 服务器启动成功: http://localhost:3000
{
  "service": "inspection-system"
}
2025-07-11 12:09:51 [INFO]: 环境: development
{
  "service": "inspection-system"
}
2025-07-11 12:10:28 [INFO]: ::1 - - [11/Jul/2025:04:10:28 +0000] "GET /api/templates HTTP/1.1" 200 1281 "-" "curl/8.7.1"
{
  "service": "inspection-system"
}
2025-07-11 12:10:43 [INFO]: ::1 - - [11/Jul/2025:04:10:43 +0000] "GET /api/templates/1 HTTP/1.1" 200 4082 "-" "curl/8.7.1"
{
  "service": "inspection-system"
}
