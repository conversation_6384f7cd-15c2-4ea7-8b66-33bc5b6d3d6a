import React, { useEffect } from 'react'
import { Navigate } from 'react-router-dom'
import { Spin } from 'antd'
import { useAuthStore } from '@/stores/auth'
import { authApi } from '@/services/auth'

interface ProtectedRouteProps {
  children: React.ReactNode
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const { isAuthenticated, user, tokens, updateUser, logout } = useAuthStore()
  const [loading, setLoading] = React.useState(true)

  useEffect(() => {
    const checkAuth = async () => {
      if (!isAuthenticated || !tokens) {
        setLoading(false)
        return
      }

      try {
        // 验证当前用户信息
        const response = await authApi.getCurrentUser()
        if (response.success && response.data) {
          updateUser(response.data.user)
        }
      } catch (error) {
        console.error('Auth check failed:', error)
        logout()
      } finally {
        setLoading(false)
      }
    }

    checkAuth()
  }, [isAuthenticated, tokens, updateUser, logout])

  if (loading) {
    return (
      <div style={{ 
        height: '100vh', 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'center' 
      }}>
        <Spin size="large" tip="加载中..." />
      </div>
    )
  }

  if (!isAuthenticated || !user) {
    return <Navigate to="/login" replace />
  }

  return <>{children}</>
}

export default ProtectedRoute
