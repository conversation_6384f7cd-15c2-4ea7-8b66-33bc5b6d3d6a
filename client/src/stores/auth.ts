import { create } from 'zustand'
import { persist } from 'zustand/middleware'

export interface User {
  id: number
  employee_id: string
  name: string
  role: 'super_admin' | 'site_admin' | 'inspector' | 'viewer'
  department?: string
  email?: string
  phone?: string
  status: 'active' | 'inactive' | 'suspended'
  created_at: string
  last_login_at?: string
}

export interface AuthTokens {
  accessToken: string
  refreshToken: string
}

interface AuthState {
  user: User | null
  tokens: AuthTokens | null
  isAuthenticated: boolean
  
  // Actions
  login: (user: User, tokens: AuthTokens) => void
  logout: () => void
  updateUser: (user: User) => void
  updateTokens: (tokens: AuthTokens) => void
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      tokens: null,
      isAuthenticated: false,

      login: (user: User, tokens: AuthTokens) => {
        set({
          user,
          tokens,
          isAuthenticated: true,
        })
      },

      logout: () => {
        set({
          user: null,
          tokens: null,
          isAuthenticated: false,
        })
      },

      updateUser: (user: User) => {
        set({ user })
      },

      updateTokens: (tokens: AuthTokens) => {
        set({ tokens })
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        tokens: state.tokens,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
)
