import { request } from './api'
import { User, AuthTokens } from '@/stores/auth'

// 登录请求参数
export interface LoginRequest {
  employeeId: string
  password: string
}

// 登录响应数据
export interface LoginResponse {
  user: User
  tokens: AuthTokens
}

// 修改密码请求参数
export interface ChangePasswordRequest {
  currentPassword: string
  newPassword: string
  confirmPassword: string
}

// 认证相关 API
export const authApi = {
  // 用户登录
  login: (data: LoginRequest) =>
    request.post<LoginResponse>('/auth/login', data),

  // 刷新令牌
  refreshToken: (refreshToken: string) =>
    request.post<{ tokens: AuthTokens }>('/auth/refresh', { refreshToken }),

  // 获取当前用户信息
  getCurrentUser: () =>
    request.get<{ user: User }>('/auth/me'),

  // 修改密码
  changePassword: (data: ChangePasswordRequest) =>
    request.put('/auth/change-password', data),

  // 登出
  logout: () =>
    request.post('/auth/logout'),
}
