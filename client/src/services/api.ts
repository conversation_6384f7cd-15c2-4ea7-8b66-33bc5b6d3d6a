import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import { message } from 'antd'
import { useAuthStore } from '@/stores/auth'

// API 响应接口
export interface ApiResponse<T = any> {
  success: boolean
  message: string
  data?: T
  error?: string
  timestamp: string
}

export interface PaginatedResponse<T> {
  data: T[]
  pagination: {
    page: number
    pageSize: number
    total: number
    totalPages: number
  }
}

// 创建 axios 实例
const createApiClient = (): AxiosInstance => {
  const client = axios.create({
    baseURL: '/api',
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json',
    },
  })

  // 请求拦截器
  client.interceptors.request.use(
    (config) => {
      const { tokens } = useAuthStore.getState()
      
      if (tokens?.accessToken) {
        config.headers.Authorization = `Bearer ${tokens.accessToken}`
      }
      
      return config
    },
    (error) => {
      return Promise.reject(error)
    }
  )

  // 响应拦截器
  client.interceptors.response.use(
    (response: AxiosResponse<ApiResponse>) => {
      return response
    },
    async (error) => {
      const { response } = error
      
      if (response?.status === 401) {
        // Token 过期或无效，尝试刷新
        const { tokens, logout, updateTokens } = useAuthStore.getState()
        
        if (tokens?.refreshToken) {
          try {
            const refreshResponse = await axios.post('/api/auth/refresh', {
              refreshToken: tokens.refreshToken,
            })
            
            if (refreshResponse.data.success) {
              const newTokens = refreshResponse.data.data.tokens
              updateTokens(newTokens)
              
              // 重新发送原请求
              const originalRequest = error.config
              originalRequest.headers.Authorization = `Bearer ${newTokens.accessToken}`
              return client.request(originalRequest)
            }
          } catch (refreshError) {
            console.error('Token refresh failed:', refreshError)
          }
        }
        
        // 刷新失败，登出用户
        logout()
        window.location.href = '/login'
        message.error('登录已过期，请重新登录')
      } else if (response?.status >= 500) {
        message.error('服务器错误，请稍后重试')
      } else if (response?.data?.message) {
        message.error(response.data.message)
      } else {
        message.error('请求失败，请检查网络连接')
      }
      
      return Promise.reject(error)
    }
  )

  return client
}

// 创建 API 客户端实例
export const apiClient = createApiClient()

// 通用 API 请求方法
export const request = {
  get: <T = any>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> =>
    apiClient.get(url, config).then(res => res.data),
    
  post: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> =>
    apiClient.post(url, data, config).then(res => res.data),
    
  put: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> =>
    apiClient.put(url, data, config).then(res => res.data),
    
  delete: <T = any>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> =>
    apiClient.delete(url, config).then(res => res.data),
    
  patch: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> =>
    apiClient.patch(url, data, config).then(res => res.data),
}

// 文件上传方法
export const uploadFile = (
  url: string,
  file: File,
  onProgress?: (progress: number) => void
): Promise<ApiResponse> => {
  const formData = new FormData()
  formData.append('file', file)
  
  return apiClient.post(url, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    onUploadProgress: (progressEvent) => {
      if (onProgress && progressEvent.total) {
        const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
        onProgress(progress)
      }
    },
  }).then(res => res.data)
}

// 下载文件方法
export const downloadFile = async (
  url: string,
  filename?: string
): Promise<void> => {
  try {
    const response = await apiClient.get(url, {
      responseType: 'blob',
    })
    
    const blob = new Blob([response.data])
    const downloadUrl = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = downloadUrl
    link.download = filename || 'download'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(downloadUrl)
  } catch (error) {
    console.error('Download failed:', error)
    message.error('文件下载失败')
  }
}
