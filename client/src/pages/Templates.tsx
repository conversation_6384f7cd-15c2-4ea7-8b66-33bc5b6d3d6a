import React, { useState } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import {
  Card,
  Table,
  Button,
  Input,
  Select,
  Space,
  Tag,
  Modal,
  Form,
  message,
  Tooltip,
  Popconfirm,
  Row,
  Col,
  Statistic,
  Badge,
  Divider,
  Typography,
  Switch,
  InputNumber,
  Tabs
} from 'antd'
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  CopyOutlined,
  SearchOutlined,
  FileTextOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  SettingOutlined,
  EyeOutlined,
  DownloadOutlined
} from '@ant-design/icons'
import { useAuthStore } from '../stores/authStore'

const { Search } = Input
const { Option } = Select
const { Title, Text } = Typography
const { TabPane } = Tabs

// 模拟数据类型
interface Template {
  id: number
  template_code: string
  template_name: string
  description: string
  site_type: string
  site_level: string
  check_items_count: number
  estimated_duration: number
  status: 'active' | 'inactive' | 'draft'
  is_default: boolean
  created_by: string
  created_at: string
  updated_at: string
  version: string
}

interface CheckItem {
  id: number
  item_code: string
  item_name: string
  category: string
  check_type: 'visual' | 'measurement' | 'test' | 'record'
  description: string
  standard: string
  is_required: boolean
  sort_order: number
}

// 模拟API函数
const templateApi = {
  getTemplates: async (params: any) => {
    // 模拟数据
    const templates: Template[] = [
      {
        id: 1,
        template_code: 'TPL-5G-001',
        template_name: '5G基站标准巡检模板',
        description: '适用于5G基站的标准巡检流程，包含设备检查、环境监测、性能测试等项目',
        site_type: '5G基站',
        site_level: 'A',
        check_items_count: 25,
        estimated_duration: 120,
        status: 'active',
        is_default: true,
        created_by: '张工程师',
        created_at: '2024-01-15T08:00:00Z',
        updated_at: '2024-01-20T10:30:00Z',
        version: 'v2.1'
      },
      {
        id: 2,
        template_code: 'TPL-4G-001',
        template_name: '4G基站常规巡检模板',
        description: '4G基站日常巡检模板，重点关注设备运行状态和信号质量',
        site_type: '4G基站',
        site_level: 'B',
        check_items_count: 18,
        estimated_duration: 90,
        status: 'active',
        is_default: false,
        created_by: '李技术员',
        created_at: '2024-01-10T09:15:00Z',
        updated_at: '2024-01-18T14:20:00Z',
        version: 'v1.5'
      },
      {
        id: 3,
        template_code: 'TPL-MICRO-001',
        template_name: '微基站快速巡检模板',
        description: '微基站快速巡检模板，适用于密集部署区域的快速检查',
        site_type: '微基站',
        site_level: 'C',
        check_items_count: 12,
        estimated_duration: 45,
        status: 'draft',
        is_default: false,
        created_by: '王维护员',
        created_at: '2024-01-22T11:00:00Z',
        updated_at: '2024-01-22T11:00:00Z',
        version: 'v1.0'
      }
    ]

    return {
      templates,
      pagination: {
        page: 1,
        pageSize: 20,
        total: templates.length,
        totalPages: 1
      }
    }
  },

  getCheckItems: async (templateId: number) => {
    // 模拟检查项目数据
    const checkItems: CheckItem[] = [
      {
        id: 1,
        item_code: 'CHK-PWR-001',
        item_name: '电源系统检查',
        category: '电源设备',
        check_type: 'visual',
        description: '检查电源设备运行状态，包括UPS、开关电源等',
        standard: '设备指示灯正常，无异响，温度正常',
        is_required: true,
        sort_order: 1
      },
      {
        id: 2,
        item_code: 'CHK-ANT-001',
        item_name: '天线系统检查',
        category: '天线设备',
        check_type: 'visual',
        description: '检查天线安装状态、连接线缆等',
        standard: '天线安装牢固，线缆连接良好，无破损',
        is_required: true,
        sort_order: 2
      },
      {
        id: 3,
        item_code: 'CHK-ENV-001',
        item_name: '环境温湿度测量',
        category: '环境监测',
        check_type: 'measurement',
        description: '测量机房温度和湿度',
        standard: '温度18-28℃，湿度40-70%',
        is_required: true,
        sort_order: 3
      }
    ]

    return checkItems
  }
}

const Templates: React.FC = () => {
  const { user } = useAuthStore()
  const queryClient = useQueryClient()
  const [searchParams, setSearchParams] = useState({
    page: 1,
    pageSize: 20,
    search: '',
    site_type: '',
    status: ''
  })
  const [isModalVisible, setIsModalVisible] = useState(false)
  const [editingTemplate, setEditingTemplate] = useState<Template | null>(null)
  const [checkItemsModalVisible, setCheckItemsModalVisible] = useState(false)
  const [selectedTemplateId, setSelectedTemplateId] = useState<number | null>(null)
  const [form] = Form.useForm()

  // 获取模板列表
  const { data: templatesData, isLoading } = useQuery(
    ['templates', searchParams],
    () => templateApi.getTemplates(searchParams),
    {
      keepPreviousData: true,
    }
  )

  // 获取检查项目
  const { data: checkItems } = useQuery(
    ['checkItems', selectedTemplateId],
    () => selectedTemplateId ? templateApi.getCheckItems(selectedTemplateId) : Promise.resolve([]),
    {
      enabled: !!selectedTemplateId,
    }
  )

  // 处理搜索
  const handleSearch = (value: string) => {
    setSearchParams(prev => ({ ...prev, search: value, page: 1 }))
  }

  // 处理筛选
  const handleFilter = (key: string, value: string) => {
    setSearchParams(prev => ({ ...prev, [key]: value, page: 1 }))
  }

  // 打开新建/编辑模态框
  const handleEdit = (template?: Template) => {
    setEditingTemplate(template || null)
    if (template) {
      form.setFieldsValue(template)
    } else {
      form.resetFields()
    }
    setIsModalVisible(true)
  }

  // 查看检查项目
  const handleViewCheckItems = (templateId: number) => {
    setSelectedTemplateId(templateId)
    setCheckItemsModalVisible(true)
  }

  // 状态标签渲染
  const renderStatusTag = (status: string) => {
    const statusConfig = {
      active: { color: 'green', text: '启用' },
      inactive: { color: 'red', text: '停用' },
      draft: { color: 'orange', text: '草稿' }
    }
    const config = statusConfig[status as keyof typeof statusConfig]
    return <Tag color={config.color}>{config.text}</Tag>
  }

  // 表格列定义
  const columns = [
    {
      title: '模板编号',
      dataIndex: 'template_code',
      key: 'template_code',
      width: 120,
      render: (text: string, record: Template) => (
        <Space>
          <Text strong>{text}</Text>
          {record.is_default && <Badge status="success" text="默认" />}
        </Space>
      )
    },
    {
      title: '模板名称',
      dataIndex: 'template_name',
      key: 'template_name',
      render: (text: string, record: Template) => (
        <div>
          <div style={{ fontWeight: 500 }}>{text}</div>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {record.description}
          </Text>
        </div>
      )
    },
    {
      title: '适用站址',
      key: 'site_info',
      width: 120,
      render: (record: Template) => (
        <div>
          <Tag color="blue">{record.site_type}</Tag>
          <Tag color="purple">{record.site_level}级</Tag>
        </div>
      )
    },
    {
      title: '检查项目',
      dataIndex: 'check_items_count',
      key: 'check_items_count',
      width: 80,
      render: (count: number) => (
        <Badge count={count} style={{ backgroundColor: '#52c41a' }} />
      )
    },
    {
      title: '预计时长',
      dataIndex: 'estimated_duration',
      key: 'estimated_duration',
      width: 80,
      render: (duration: number) => `${duration}分钟`
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: renderStatusTag
    },
    {
      title: '版本',
      dataIndex: 'version',
      key: 'version',
      width: 80
    },
    {
      title: '创建人',
      dataIndex: 'created_by',
      key: 'created_by',
      width: 100
    },
    {
      title: '操作',
      key: 'actions',
      width: 200,
      render: (record: Template) => (
        <Space>
          <Tooltip title="查看检查项目">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => handleViewCheckItems(record.id)}
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          <Tooltip title="复制">
            <Button
              type="text"
              icon={<CopyOutlined />}
            />
          </Tooltip>
          <Tooltip title="导出">
            <Button
              type="text"
              icon={<DownloadOutlined />}
            />
          </Tooltip>
          {user?.role === 'super_admin' && (
            <Popconfirm
              title="确定要删除这个模板吗？"
              onConfirm={() => message.success('删除成功')}
            >
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
              />
            </Popconfirm>
          )}
        </Space>
      )
    }
  ]

  return (
    <div style={{ padding: '24px' }}>
      {/* 页面标题和统计 */}
      <div style={{ marginBottom: '24px' }}>
        <Title level={2}>
          <FileTextOutlined /> 巡检模板配置
        </Title>

        <Row gutter={16} style={{ marginTop: '16px' }}>
          <Col span={6}>
            <Card>
              <Statistic
                title="模板总数"
                value={templatesData?.pagination.total || 0}
                prefix={<FileTextOutlined />}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="启用模板"
                value={templatesData?.templates.filter(t => t.status === 'active').length || 0}
                prefix={<CheckCircleOutlined />}
                valueStyle={{ color: '#3f8600' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="草稿模板"
                value={templatesData?.templates.filter(t => t.status === 'draft').length || 0}
                prefix={<ClockCircleOutlined />}
                valueStyle={{ color: '#cf1322' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="默认模板"
                value={templatesData?.templates.filter(t => t.is_default).length || 0}
                prefix={<SettingOutlined />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
        </Row>
      </div>

      {/* 搜索和筛选 */}
      <Card style={{ marginBottom: '16px' }}>
        <Row gutter={16} align="middle">
          <Col span={8}>
            <Search
              placeholder="搜索模板名称或编号"
              allowClear
              onSearch={handleSearch}
              style={{ width: '100%' }}
            />
          </Col>
          <Col span={4}>
            <Select
              placeholder="站址类型"
              allowClear
              style={{ width: '100%' }}
              onChange={(value) => handleFilter('site_type', value || '')}
            >
              <Option value="5G基站">5G基站</Option>
              <Option value="4G基站">4G基站</Option>
              <Option value="微基站">微基站</Option>
              <Option value="室分系统">室分系统</Option>
            </Select>
          </Col>
          <Col span={4}>
            <Select
              placeholder="状态"
              allowClear
              style={{ width: '100%' }}
              onChange={(value) => handleFilter('status', value || '')}
            >
              <Option value="active">启用</Option>
              <Option value="inactive">停用</Option>
              <Option value="draft">草稿</Option>
            </Select>
          </Col>
          <Col span={8}>
            <Space>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => handleEdit()}
              >
                新建模板
              </Button>
              <Button icon={<DownloadOutlined />}>
                批量导出
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 模板列表 */}
      <Card>
        <Table
          columns={columns}
          dataSource={templatesData?.templates}
          rowKey="id"
          loading={isLoading}
          pagination={{
            current: searchParams.page,
            pageSize: searchParams.pageSize,
            total: templatesData?.pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
            onChange: (page, pageSize) => {
              setSearchParams(prev => ({ ...prev, page, pageSize }))
            }
          }}
        />
      </Card>

      {/* 检查项目模态框 */}
      <Modal
        title="检查项目详情"
        visible={checkItemsModalVisible}
        onCancel={() => setCheckItemsModalVisible(false)}
        footer={null}
        width={800}
      >
        <Table
          dataSource={checkItems}
          rowKey="id"
          pagination={false}
          size="small"
          columns={[
            {
              title: '项目编号',
              dataIndex: 'item_code',
              width: 120
            },
            {
              title: '检查项目',
              dataIndex: 'item_name',
              render: (text: string, record: CheckItem) => (
                <div>
                  <div>{text}</div>
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    {record.description}
                  </Text>
                </div>
              )
            },
            {
              title: '类别',
              dataIndex: 'category',
              width: 100
            },
            {
              title: '检查方式',
              dataIndex: 'check_type',
              width: 100,
              render: (type: string) => {
                const typeMap = {
                  visual: '目视检查',
                  measurement: '测量',
                  test: '测试',
                  record: '记录'
                }
                return typeMap[type as keyof typeof typeMap]
              }
            },
            {
              title: '必检',
              dataIndex: 'is_required',
              width: 60,
              render: (required: boolean) => required ? '是' : '否'
            }
          ]}
        />
      </Modal>
    </div>
  )
}

export default Templates
