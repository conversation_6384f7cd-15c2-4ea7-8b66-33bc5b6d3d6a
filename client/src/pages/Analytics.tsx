import React, { useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import {
  Card,
  Row,
  Col,
  Statistic,
  Typography,
  Select,
  DatePicker,
  Space,
  Table,
  Tag,
  Progress,
  Tabs,
  List,
  Avatar,
  Badge,
  Tooltip,
  Button
} from 'antd'
import {
  <PERSON><PERSON><PERSON>Outlined,
  <PERSON><PERSON><PERSON>Outlined,
  Pie<PERSON><PERSON>Outlined,
  TrendingUpOutlined,
  TrendingDownOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  ClockCircleOutlined,
  EnvironmentOutlined,
  UserOutlined,
  CalendarOutlined,
  FileTextOutlined,
  WarningOutlined,
  DownloadOutlined
} from '@ant-design/icons'
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  Legend,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON>,
  Line,
  Pie<PERSON><PERSON>,
  Pie,
  Cell
} from 'recharts'
import dayjs from 'dayjs'

const { Title, Text } = Typography
const { Option } = Select
const { RangePicker } = DatePicker
const { TabPane } = Tabs

// 数据类型定义
interface AnalyticsData {
  overview: {
    totalTasks: number
    completedTasks: number
    completionRate: number
    avgDuration: number
    totalSites: number
    activeSites: number
    issuesFound: number
    criticalIssues: number
  }
  taskTrend: Array<{
    date: string
    completed: number
    created: number
    pending: number
  }>
  siteStatus: Array<{
    name: string
    value: number
    color: string
  }>
  performanceByUser: Array<{
    user: string
    completed: number
    avgDuration: number
    efficiency: number
  }>
  issuesByCategory: Array<{
    category: string
    count: number
    severity: 'low' | 'medium' | 'high' | 'critical'
  }>
  recentIssues: Array<{
    id: number
    site_name: string
    issue_type: string
    severity: string
    description: string
    found_at: string
    status: string
  }>
}

// 模拟API
const analyticsApi = {
  getAnalyticsData: async (params: any): Promise<AnalyticsData> => {
    return {
      overview: {
        totalTasks: 156,
        completedTasks: 142,
        completionRate: 91.0,
        avgDuration: 95,
        totalSites: 45,
        activeSites: 43,
        issuesFound: 23,
        criticalIssues: 3
      },
      taskTrend: [
        { date: '01-08', completed: 12, created: 15, pending: 3 },
        { date: '01-09', completed: 18, created: 12, pending: 2 },
        { date: '01-10', completed: 15, created: 20, pending: 5 },
        { date: '01-11', completed: 22, created: 18, pending: 1 },
        { date: '01-12', completed: 19, created: 16, pending: 4 },
        { date: '01-13', completed: 25, created: 22, pending: 2 },
        { date: '01-14', completed: 21, created: 19, pending: 3 }
      ],
      siteStatus: [
        { name: '正常运行', value: 38, color: '#52c41a' },
        { name: '维护中', value: 5, color: '#faad14' },
        { name: '故障', value: 2, color: '#ff4d4f' }
      ],
      performanceByUser: [
        { user: '李维护员', completed: 28, avgDuration: 85, efficiency: 95 },
        { user: '王技术员', completed: 25, avgDuration: 92, efficiency: 88 },
        { user: '陈维护员', completed: 22, avgDuration: 105, efficiency: 82 },
        { user: '张工程师', completed: 20, avgDuration: 78, efficiency: 98 }
      ],
      issuesByCategory: [
        { category: '电源设备', count: 8, severity: 'medium' },
        { category: '天线系统', count: 5, severity: 'high' },
        { category: '环境监测', count: 6, severity: 'low' },
        { category: '网络设备', count: 4, severity: 'critical' }
      ],
      recentIssues: [
        {
          id: 1,
          site_name: '北京朝阳5G基站',
          issue_type: '设备故障',
          severity: 'critical',
          description: 'UPS电源异常告警',
          found_at: '2024-01-15T10:30:00Z',
          status: 'pending'
        },
        {
          id: 2,
          site_name: '上海浦东5G基站',
          issue_type: '环境异常',
          severity: 'medium',
          description: '机房温度偏高',
          found_at: '2024-01-15T09:15:00Z',
          status: 'in_progress'
        },
        {
          id: 3,
          site_name: '广州天河微基站',
          issue_type: '信号质量',
          severity: 'low',
          description: '信号强度略低于标准',
          found_at: '2024-01-14T16:20:00Z',
          status: 'resolved'
        }
      ]
    }
  }
}

const Analytics: React.FC = () => {
  const [dateRange, setDateRange] = useState<any>(null)
  const [selectedSiteType, setSelectedSiteType] = useState<string>('')

  // 获取分析数据
  const { data: analyticsData, isLoading } = useQuery(
    ['analytics', dateRange, selectedSiteType],
    () => analyticsApi.getAnalyticsData({ dateRange, selectedSiteType }),
    {
      keepPreviousData: true,
    }
  )

  // 渲染严重程度标签
  const renderSeverityTag = (severity: string) => {
    const severityConfig = {
      low: { color: 'green', text: '低' },
      medium: { color: 'orange', text: '中' },
      high: { color: 'red', text: '高' },
      critical: { color: 'red', text: '严重' }
    }
    const config = severityConfig[severity as keyof typeof severityConfig]
    return <Tag color={config.color}>{config.text}</Tag>
  }

  // 渲染状态标签
  const renderStatusTag = (status: string) => {
    const statusConfig = {
      pending: { color: 'orange', text: '待处理' },
      in_progress: { color: 'blue', text: '处理中' },
      resolved: { color: 'green', text: '已解决' }
    }
    const config = statusConfig[status as keyof typeof statusConfig]
    return <Tag color={config.color}>{config.text}</Tag>
  }

  const COLORS = ['#52c41a', '#faad14', '#ff4d4f', '#1890ff']

  return (
    <div style={{ padding: '24px' }}>
      {/* 页面标题和筛选 */}
      <div style={{ marginBottom: '24px' }}>
        <Row justify="space-between" align="middle">
          <Col>
            <Title level={2}>
              <BarChartOutlined /> 数据分析
            </Title>
          </Col>
          <Col>
            <Space>
              <Select
                placeholder="站址类型"
                allowClear
                style={{ width: 120 }}
                onChange={setSelectedSiteType}
              >
                <Option value="5G基站">5G基站</Option>
                <Option value="4G基站">4G基站</Option>
                <Option value="微基站">微基站</Option>
              </Select>
              <RangePicker
                placeholder={['开始日期', '结束日期']}
                onChange={setDateRange}
              />
              <Button icon={<DownloadOutlined />}>导出报告</Button>
            </Space>
          </Col>
        </Row>
      </div>

      {/* 概览统计 */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="任务完成率"
              value={analyticsData?.overview.completionRate || 0}
              precision={1}
              suffix="%"
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="平均执行时长"
              value={analyticsData?.overview.avgDuration || 0}
              suffix="分钟"
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="发现问题"
              value={analyticsData?.overview.issuesFound || 0}
              prefix={<ExclamationCircleOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="严重问题"
              value={analyticsData?.overview.criticalIssues || 0}
              prefix={<WarningOutlined />}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 图表区域 */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={12}>
          <Card title="任务趋势分析" extra={<LineChartOutlined />}>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={analyticsData?.taskTrend}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <RechartsTooltip />
                <Legend />
                <Line type="monotone" dataKey="completed" stroke="#52c41a" name="已完成" />
                <Line type="monotone" dataKey="created" stroke="#1890ff" name="新建" />
                <Line type="monotone" dataKey="pending" stroke="#faad14" name="待处理" />
              </LineChart>
            </ResponsiveContainer>
          </Card>
        </Col>
        <Col span={12}>
          <Card title="站址状态分布" extra={<PieChartOutlined />}>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={analyticsData?.siteStatus}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {analyticsData?.siteStatus.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <RechartsTooltip />
              </PieChart>
            </ResponsiveContainer>
          </Card>
        </Col>
      </Row>

      {/* 详细数据 */}
      <Tabs defaultActiveKey="1">
        <TabPane tab="人员绩效" key="1">
          <Card>
            <Table
              dataSource={analyticsData?.performanceByUser}
              rowKey="user"
              pagination={false}
              columns={[
                {
                  title: '执行人',
                  dataIndex: 'user',
                  key: 'user',
                  render: (text: string) => (
                    <Space>
                      <Avatar size="small" icon={<UserOutlined />} />
                      <Text>{text}</Text>
                    </Space>
                  )
                },
                {
                  title: '完成任务数',
                  dataIndex: 'completed',
                  key: 'completed',
                  render: (count: number) => <Badge count={count} style={{ backgroundColor: '#52c41a' }} />
                },
                {
                  title: '平均时长',
                  dataIndex: 'avgDuration',
                  key: 'avgDuration',
                  render: (duration: number) => `${duration}分钟`
                },
                {
                  title: '效率评分',
                  dataIndex: 'efficiency',
                  key: 'efficiency',
                  render: (efficiency: number) => (
                    <Progress 
                      percent={efficiency} 
                      size="small" 
                      status={efficiency >= 90 ? 'success' : efficiency >= 80 ? 'active' : 'exception'}
                    />
                  )
                }
              ]}
            />
          </Card>
        </TabPane>
        <TabPane tab="问题分析" key="2">
          <Row gutter={16}>
            <Col span={12}>
              <Card title="问题分类统计">
                <List
                  dataSource={analyticsData?.issuesByCategory}
                  renderItem={(item) => (
                    <List.Item>
                      <List.Item.Meta
                        title={
                          <Space>
                            <Text>{item.category}</Text>
                            {renderSeverityTag(item.severity)}
                          </Space>
                        }
                        description={`发现 ${item.count} 个问题`}
                      />
                      <Badge count={item.count} style={{ backgroundColor: '#1890ff' }} />
                    </List.Item>
                  )}
                />
              </Card>
            </Col>
            <Col span={12}>
              <Card title="最近发现的问题">
                <List
                  dataSource={analyticsData?.recentIssues}
                  renderItem={(item) => (
                    <List.Item>
                      <List.Item.Meta
                        title={
                          <Space>
                            <Text>{item.site_name}</Text>
                            {renderSeverityTag(item.severity)}
                            {renderStatusTag(item.status)}
                          </Space>
                        }
                        description={
                          <div>
                            <div>{item.description}</div>
                            <Text type="secondary" style={{ fontSize: '12px' }}>
                              {dayjs(item.found_at).format('YYYY-MM-DD HH:mm')}
                            </Text>
                          </div>
                        }
                      />
                    </List.Item>
                  )}
                />
              </Card>
            </Col>
          </Row>
        </TabPane>
      </Tabs>
    </div>
  )
}

export default Analytics
