import React, { useState } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import {
  Card,
  Tabs,
  Table,
  Button,
  Input,
  Select,
  Space,
  Tag,
  Modal,
  Form,
  message,
  Tooltip,
  Popconfirm,
  Row,
  Col,
  Typography,
  Switch,
  InputNumber,
  Avatar,
  Badge,
  Divider,
  List,
  Upload,
  Alert
} from 'antd'
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SearchOutlined,
  SettingOutlined,
  UserOutlined,
  TeamOutlined,
  SecurityScanOutlined,
  DatabaseOutlined,
  NotificationOutlined,
  UploadOutlined,
  DownloadOutlined,
  KeyOutlined,
  MailOutlined,
  PhoneOutlined,
  SaveOutlined,
  ReloadOutlined
} from '@ant-design/icons'
import { useAuthStore } from '../stores/authStore'

const { Search } = Input
const { Option } = Select
const { Title, Text } = Typography
const { TabPane } = Tabs
const { TextArea } = Input

// 数据类型定义
interface User {
  id: number
  employee_id: string
  name: string
  email: string
  phone: string
  department: string
  role: 'super_admin' | 'admin' | 'user'
  status: 'active' | 'inactive'
  last_login_at?: string
  created_at: string
}

interface SystemConfig {
  id: string
  name: string
  value: string
  description: string
  type: 'string' | 'number' | 'boolean' | 'json'
  category: string
}

// 模拟API
const settingsApi = {
  getUsers: async (params: any) => {
    const users: User[] = [
      {
        id: 1,
        employee_id: 'admin',
        name: '系统管理员',
        email: '<EMAIL>',
        phone: '13800138000',
        department: '信息技术部',
        role: 'super_admin',
        status: 'active',
        last_login_at: '2024-01-15T10:30:00Z',
        created_at: '2024-01-01T00:00:00Z'
      },
      {
        id: 2,
        employee_id: 'manager001',
        name: '张管理员',
        email: '<EMAIL>',
        phone: '13800138001',
        department: '运维部',
        role: 'admin',
        status: 'active',
        last_login_at: '2024-01-15T09:15:00Z',
        created_at: '2024-01-02T00:00:00Z'
      },
      {
        id: 3,
        employee_id: 'user001',
        name: '李维护员',
        email: '<EMAIL>',
        phone: '13800138002',
        department: '运维部',
        role: 'user',
        status: 'active',
        last_login_at: '2024-01-15T08:45:00Z',
        created_at: '2024-01-03T00:00:00Z'
      }
    ]

    return {
      users,
      pagination: {
        page: 1,
        pageSize: 20,
        total: users.length,
        totalPages: 1
      }
    }
  },

  getSystemConfigs: async () => {
    const configs: SystemConfig[] = [
      {
        id: 'system.name',
        name: '系统名称',
        value: 'IoT巡检管理系统',
        description: '系统显示名称',
        type: 'string',
        category: '基本设置'
      },
      {
        id: 'system.version',
        name: '系统版本',
        value: 'v1.0.0',
        description: '当前系统版本号',
        type: 'string',
        category: '基本设置'
      },
      {
        id: 'task.auto_assign',
        name: '自动分配任务',
        value: 'true',
        description: '是否启用任务自动分配功能',
        type: 'boolean',
        category: '任务设置'
      },
      {
        id: 'notification.email_enabled',
        name: '邮件通知',
        value: 'true',
        description: '是否启用邮件通知功能',
        type: 'boolean',
        category: '通知设置'
      },
      {
        id: 'notification.sms_enabled',
        name: '短信通知',
        value: 'false',
        description: '是否启用短信通知功能',
        type: 'boolean',
        category: '通知设置'
      }
    ]

    return configs
  }
}

const Settings: React.FC = () => {
  const { user } = useAuthStore()
  const queryClient = useQueryClient()
  const [userSearchParams, setUserSearchParams] = useState({
    page: 1,
    pageSize: 20,
    search: '',
    role: '',
    status: ''
  })
  const [isUserModalVisible, setIsUserModalVisible] = useState(false)
  const [editingUser, setEditingUser] = useState<User | null>(null)
  const [userForm] = Form.useForm()
  const [configForm] = Form.useForm()

  // 获取用户列表
  const { data: usersData, isLoading: usersLoading } = useQuery(
    ['users', userSearchParams],
    () => settingsApi.getUsers(userSearchParams),
    {
      keepPreviousData: true,
    }
  )

  // 获取系统配置
  const { data: systemConfigs, isLoading: configsLoading } = useQuery(
    ['systemConfigs'],
    () => settingsApi.getSystemConfigs()
  )

  // 处理用户搜索
  const handleUserSearch = (value: string) => {
    setUserSearchParams(prev => ({ ...prev, search: value, page: 1 }))
  }

  // 处理用户筛选
  const handleUserFilter = (key: string, value: string) => {
    setUserSearchParams(prev => ({ ...prev, [key]: value, page: 1 }))
  }

  // 打开用户编辑模态框
  const handleEditUser = (user?: User) => {
    setEditingUser(user || null)
    if (user) {
      userForm.setFieldsValue(user)
    } else {
      userForm.resetFields()
    }
    setIsUserModalVisible(true)
  }

  // 角色标签渲染
  const renderRoleTag = (role: string) => {
    const roleConfig = {
      super_admin: { color: 'red', text: '超级管理员' },
      admin: { color: 'blue', text: '管理员' },
      user: { color: 'green', text: '普通用户' }
    }
    const config = roleConfig[role as keyof typeof roleConfig]
    return <Tag color={config.color}>{config.text}</Tag>
  }

  // 状态标签渲染
  const renderStatusTag = (status: string) => {
    const statusConfig = {
      active: { color: 'green', text: '启用' },
      inactive: { color: 'red', text: '停用' }
    }
    const config = statusConfig[status as keyof typeof statusConfig]
    return <Tag color={config.color}>{config.text}</Tag>
  }

  // 用户表格列定义
  const userColumns = [
    {
      title: '用户信息',
      key: 'user_info',
      render: (record: User) => (
        <Space>
          <Avatar icon={<UserOutlined />} />
          <div>
            <div style={{ fontWeight: 500 }}>{record.name}</div>
            <Text type="secondary">{record.employee_id}</Text>
          </div>
        </Space>
      )
    },
    {
      title: '联系方式',
      key: 'contact',
      render: (record: User) => (
        <div>
          <div><MailOutlined /> {record.email}</div>
          <div><PhoneOutlined /> {record.phone}</div>
        </div>
      )
    },
    {
      title: '部门',
      dataIndex: 'department',
      key: 'department'
    },
    {
      title: '角色',
      dataIndex: 'role',
      key: 'role',
      render: renderRoleTag
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: renderStatusTag
    },
    {
      title: '最后登录',
      dataIndex: 'last_login_at',
      key: 'last_login_at',
      render: (date: string) => date ? new Date(date).toLocaleString() : '从未登录'
    },
    {
      title: '操作',
      key: 'actions',
      render: (record: User) => (
        <Space>
          <Tooltip title="编辑">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEditUser(record)}
            />
          </Tooltip>
          <Tooltip title="重置密码">
            <Button
              type="text"
              icon={<KeyOutlined />}
              onClick={() => message.success('密码重置成功')}
            />
          </Tooltip>
          {user?.role === 'super_admin' && record.id !== user.id && (
            <Popconfirm
              title="确定要删除这个用户吗？"
              onConfirm={() => message.success('删除成功')}
            >
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
              />
            </Popconfirm>
          )}
        </Space>
      )
    }
  ]

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>
        <SettingOutlined /> 系统设置
      </Title>

      <Tabs defaultActiveKey="1">
        {/* 用户管理 */}
        <TabPane tab={<span><UserOutlined />用户管理</span>} key="1">
          <Card>
            <Row gutter={16} align="middle" style={{ marginBottom: '16px' }}>
              <Col span={6}>
                <Search
                  placeholder="搜索用户名或工号"
                  allowClear
                  onSearch={handleUserSearch}
                />
              </Col>
              <Col span={4}>
                <Select
                  placeholder="角色"
                  allowClear
                  style={{ width: '100%' }}
                  onChange={(value) => handleUserFilter('role', value || '')}
                >
                  <Option value="super_admin">超级管理员</Option>
                  <Option value="admin">管理员</Option>
                  <Option value="user">普通用户</Option>
                </Select>
              </Col>
              <Col span={4}>
                <Select
                  placeholder="状态"
                  allowClear
                  style={{ width: '100%' }}
                  onChange={(value) => handleUserFilter('status', value || '')}
                >
                  <Option value="active">启用</Option>
                  <Option value="inactive">停用</Option>
                </Select>
              </Col>
              <Col span={10}>
                <Space>
                  <Button
                    type="primary"
                    icon={<PlusOutlined />}
                    onClick={() => handleEditUser()}
                  >
                    新建用户
                  </Button>
                  <Button icon={<UploadOutlined />}>
                    批量导入
                  </Button>
                  <Button icon={<DownloadOutlined />}>
                    导出用户
                  </Button>
                </Space>
              </Col>
            </Row>

            <Table
              columns={userColumns}
              dataSource={usersData?.users}
              rowKey="id"
              loading={usersLoading}
              pagination={{
                current: userSearchParams.page,
                pageSize: userSearchParams.pageSize,
                total: usersData?.pagination.total,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 条记录`,
                onChange: (page, pageSize) => {
                  setUserSearchParams(prev => ({ ...prev, page, pageSize }))
                }
              }}
            />
          </Card>
        </TabPane>

        {/* 系统配置 */}
        <TabPane tab={<span><DatabaseOutlined />系统配置</span>} key="2">
          <Row gutter={16}>
            <Col span={24}>
              <Card
                title="系统参数配置"
                extra={
                  <Space>
                    <Button icon={<ReloadOutlined />}>重置</Button>
                    <Button type="primary" icon={<SaveOutlined />}>保存配置</Button>
                  </Space>
                }
              >
                <Form
                  form={configForm}
                  layout="vertical"
                  initialValues={systemConfigs?.reduce((acc, config) => {
                    acc[config.id] = config.type === 'boolean' ? config.value === 'true' : config.value
                    return acc
                  }, {} as any)}
                >
                  {systemConfigs?.reduce((acc, config) => {
                    if (!acc[config.category]) {
                      acc[config.category] = []
                    }
                    acc[config.category].push(config)
                    return acc
                  }, {} as Record<string, SystemConfig[]>) &&
                    Object.entries(
                      systemConfigs?.reduce((acc, config) => {
                        if (!acc[config.category]) {
                          acc[config.category] = []
                        }
                        acc[config.category].push(config)
                        return acc
                      }, {} as Record<string, SystemConfig[]>) || {}
                    ).map(([category, configs]) => (
                      <div key={category}>
                        <Divider orientation="left">{category}</Divider>
                        <Row gutter={16}>
                          {configs.map((config) => (
                            <Col span={12} key={config.id}>
                              <Form.Item
                                name={config.id}
                                label={config.name}
                                tooltip={config.description}
                              >
                                {config.type === 'boolean' ? (
                                  <Switch />
                                ) : config.type === 'number' ? (
                                  <InputNumber style={{ width: '100%' }} />
                                ) : (
                                  <Input />
                                )}
                              </Form.Item>
                            </Col>
                          ))}
                        </Row>
                      </div>
                    ))
                  }
                </Form>
              </Card>
            </Col>
          </Row>
        </TabPane>

        {/* 安全设置 */}
        <TabPane tab={<span><SecurityScanOutlined />安全设置</span>} key="3">
          <Row gutter={16}>
            <Col span={12}>
              <Card title="密码策略">
                <List>
                  <List.Item>
                    <List.Item.Meta
                      title="密码最小长度"
                      description="设置用户密码的最小长度要求"
                    />
                    <InputNumber min={6} max={20} defaultValue={8} />
                  </List.Item>
                  <List.Item>
                    <List.Item.Meta
                      title="密码复杂度"
                      description="要求密码包含大小写字母、数字和特殊字符"
                    />
                    <Switch defaultChecked />
                  </List.Item>
                  <List.Item>
                    <List.Item.Meta
                      title="密码有效期"
                      description="密码过期时间（天）"
                    />
                    <InputNumber min={30} max={365} defaultValue={90} />
                  </List.Item>
                </List>
              </Card>
            </Col>
            <Col span={12}>
              <Card title="登录安全">
                <List>
                  <List.Item>
                    <List.Item.Meta
                      title="登录失败锁定"
                      description="连续登录失败后锁定账户"
                    />
                    <Switch defaultChecked />
                  </List.Item>
                  <List.Item>
                    <List.Item.Meta
                      title="最大失败次数"
                      description="触发锁定的最大失败次数"
                    />
                    <InputNumber min={3} max={10} defaultValue={5} />
                  </List.Item>
                  <List.Item>
                    <List.Item.Meta
                      title="锁定时间"
                      description="账户锁定时间（分钟）"
                    />
                    <InputNumber min={5} max={60} defaultValue={15} />
                  </List.Item>
                </List>
              </Card>
            </Col>
          </Row>
        </TabPane>

        {/* 通知设置 */}
        <TabPane tab={<span><NotificationOutlined />通知设置</span>} key="4">
          <Card title="通知配置">
            <Alert
              message="通知功能配置"
              description="配置系统各种通知方式的参数和模板"
              type="info"
              showIcon
              style={{ marginBottom: '16px' }}
            />
            <Form layout="vertical">
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item label="邮件服务器" name="email_server">
                    <Input placeholder="smtp.company.com" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item label="邮件端口" name="email_port">
                    <InputNumber style={{ width: '100%' }} placeholder="587" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item label="发送邮箱" name="email_from">
                    <Input placeholder="<EMAIL>" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item label="邮箱密码" name="email_password">
                    <Input.Password placeholder="邮箱授权码" />
                  </Form.Item>
                </Col>
                <Col span={24}>
                  <Form.Item label="短信接口配置" name="sms_config">
                    <TextArea rows={4} placeholder="短信服务商API配置信息" />
                  </Form.Item>
                </Col>
              </Row>
              <Form.Item>
                <Space>
                  <Button type="primary">保存配置</Button>
                  <Button>测试连接</Button>
                </Space>
              </Form.Item>
            </Form>
          </Card>
        </TabPane>
      </Tabs>

      {/* 用户编辑模态框 */}
      <Modal
        title={editingUser ? '编辑用户' : '新建用户'}
        visible={isUserModalVisible}
        onCancel={() => setIsUserModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={userForm}
          layout="vertical"
          onFinish={(values) => {
            message.success(editingUser ? '用户更新成功' : '用户创建成功')
            setIsUserModalVisible(false)
          }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="工号"
                name="employee_id"
                rules={[{ required: true, message: '请输入工号' }]}
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="姓名"
                name="name"
                rules={[{ required: true, message: '请输入姓名' }]}
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="邮箱"
                name="email"
                rules={[
                  { required: true, message: '请输入邮箱' },
                  { type: 'email', message: '请输入有效的邮箱地址' }
                ]}
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="手机号"
                name="phone"
                rules={[{ required: true, message: '请输入手机号' }]}
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="部门"
                name="department"
                rules={[{ required: true, message: '请选择部门' }]}
              >
                <Select>
                  <Option value="信息技术部">信息技术部</Option>
                  <Option value="运维部">运维部</Option>
                  <Option value="工程部">工程部</Option>
                  <Option value="质量部">质量部</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="角色"
                name="role"
                rules={[{ required: true, message: '请选择角色' }]}
              >
                <Select>
                  <Option value="user">普通用户</Option>
                  <Option value="admin">管理员</Option>
                  {user?.role === 'super_admin' && (
                    <Option value="super_admin">超级管理员</Option>
                  )}
                </Select>
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item
                label="状态"
                name="status"
                valuePropName="checked"
                getValueFromEvent={(checked) => checked ? 'active' : 'inactive'}
                getValueProps={(value) => ({ checked: value === 'active' })}
              >
                <Switch checkedChildren="启用" unCheckedChildren="停用" />
              </Form.Item>
            </Col>
          </Row>
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                {editingUser ? '更新' : '创建'}
              </Button>
              <Button onClick={() => setIsUserModalVisible(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default Settings
