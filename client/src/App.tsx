import React from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'
import { QueryClient, QueryClientProvider } from 'react-query'
import { App as AntdApp } from 'antd'

import Layout from '@/components/Layout'
import Login from '@/pages/Login'
import Dashboard from '@/pages/Dashboard'
import Users from '@/pages/Users'
import Devices from '@/pages/Devices'
import Sites from '@/pages/Sites'
import Templates from '@/pages/Templates'
import Tasks from '@/pages/Tasks'
import Analytics from '@/pages/Analytics'
import Statistics from '@/pages/Statistics'
import Settings from '@/pages/Settings'
import NotFound from '@/pages/NotFound'

import { useAuthStore } from '@/stores/auth'
import ProtectedRoute from '@/components/ProtectedRoute'

// 创建 React Query 客户端
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
      staleTime: 5 * 60 * 1000, // 5分钟
    },
  },
})

const App: React.FC = () => {
  const { isAuthenticated } = useAuthStore()

  return (
    <QueryClientProvider client={queryClient}>
      <AntdApp>
        <Routes>
          {/* 登录页面 */}
          <Route 
            path="/login" 
            element={
              isAuthenticated ? <Navigate to="/" replace /> : <Login />
            } 
          />
          
          {/* 受保护的路由 */}
          <Route 
            path="/*" 
            element={
              <ProtectedRoute>
                <Layout>
                  <Routes>
                    <Route path="/" element={<Dashboard />} />
                    <Route path="/dashboard" element={<Dashboard />} />
                    <Route path="/users" element={<Users />} />
                    <Route path="/devices" element={<Devices />} />
                    <Route path="/sites" element={<Sites />} />
                    <Route path="/templates" element={<Templates />} />
                    <Route path="/tasks" element={<Tasks />} />
                    <Route path="/analytics" element={<Analytics />} />
                    <Route path="/statistics" element={<Statistics />} />
                    <Route path="/settings" element={<Settings />} />
                    <Route path="*" element={<NotFound />} />
                  </Routes>
                </Layout>
              </ProtectedRoute>
            } 
          />
        </Routes>
      </AntdApp>
    </QueryClientProvider>
  )
}

export default App
