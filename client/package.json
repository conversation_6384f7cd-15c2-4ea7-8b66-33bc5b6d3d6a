{"name": "inspection-client", "version": "1.0.0", "description": "站址巡检系统前端应用", "private": true, "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint src --ext ts,tsx --fix"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "antd": "^5.12.8", "@ant-design/icons": "^5.2.6", "axios": "^1.6.2", "dayjs": "^1.11.10", "socket.io-client": "^4.7.4", "zustand": "^4.4.7", "react-query": "^3.39.3", "recharts": "^2.8.0", "ahooks": "^3.7.8", "classnames": "^2.3.2", "lodash-es": "^4.17.21"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@types/lodash-es": "^4.17.12", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "typescript": "^5.2.2", "vite": "^5.0.8", "less": "^4.2.0"}}