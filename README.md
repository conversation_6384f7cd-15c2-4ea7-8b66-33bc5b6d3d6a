# 站址巡检系统 - 智能安全帽语音交互后台管理系统

## 项目概述

本项目是一个基于智能安全帽的语音交互站址巡检系统的后台管理平台。系统支持全语音操作，通过语音识别、语义解析技术完成标准化数据采集，实现巡检任务的自动化管理和数据分析。

## 技术架构

### 后端技术栈
- **运行环境**: Node.js 18+ 
- **框架**: Express + TypeScript
- **数据库**: PostgreSQL + Redis
- **文件存储**: MinIO
- **消息通信**: MQTT + WebSocket
- **认证**: JWT + RBAC权限模型
- **ORM**: Knex.js
- **日志**: Winston
- **容器化**: Docker + Docker Compose

### 前端技术栈
- **框架**: React 18 + TypeScript
- **构建工具**: Vite
- **UI组件**: Ant Design 5
- **状态管理**: Zustand
- **数据请求**: React Query + Axios
- **路由**: React Router 6
- **图表**: Recharts

## 项目结构

```
├── server/                 # 后端服务
│   ├── src/
│   │   ├── config/        # 配置文件
│   │   ├── controllers/   # 控制器
│   │   ├── middleware/    # 中间件
│   │   ├── models/        # 数据模型
│   │   ├── routes/        # 路由定义
│   │   ├── services/      # 业务逻辑
│   │   ├── utils/         # 工具函数
│   │   └── types/         # 类型定义
│   ├── database/
│   │   ├── migrations/    # 数据库迁移
│   │   └── seeds/         # 种子数据
│   └── config/            # 配置文件
├── client/                # 前端应用
│   ├── src/
│   │   ├── components/    # 通用组件
│   │   ├── pages/         # 页面组件
│   │   ├── services/      # API服务
│   │   ├── stores/        # 状态管理
│   │   ├── utils/         # 工具函数
│   │   └── types/         # 类型定义
│   └── public/            # 静态资源
├── docker-compose.yml     # Docker编排文件
└── README.md             # 项目说明
```

## 核心功能模块

### 1. 用户权限管理
- 多角色权限控制（超级管理员、站点管理员、巡检员、查看者）
- 用户注册、登录、权限分配
- 部门组织架构管理

### 2. 设备管理
- 智能安全帽设备注册和绑定
- 设备状态实时监控
- 设备配置下发和固件升级

### 3. 站址管理
- 站址信息维护和地图展示
- 设备清单和技术参数管理
- 巡检历史记录查询

### 4. 巡检流程配置
- 巡检模板创建和编辑
- 检查项目标准化定义
- 异常判定规则配置

### 5. 任务管理
- 巡检任务创建和分配
- 任务执行进度跟踪
- 任务完成情况统计

### 6. 数据管理
- 巡检数据接收和存储
- 音频、图片、视频文件管理
- 数据查询和导出功能

### 7. 统计分析
- 巡检完成率和质量分析
- 异常趋势和热点区域分析
- 设备使用率统计

### 8. 系统配置
- 语音识别参数配置
- 异常关键词和告警规则
- 系统参数和备份策略

## 快速开始

### 环境要求
- Node.js 18+
- PostgreSQL 12+
- Redis 6+
- Docker & Docker Compose (可选)

### 使用 Docker 启动（推荐）

1. 克隆项目
```bash
git clone <repository-url>
cd AISafeExplore
```

2. 启动所有服务
```bash
docker-compose up -d
```

3. 访问应用
- 前端应用: http://localhost
- 后端API: http://localhost:3000
- MinIO控制台: http://localhost:9001

### 本地开发启动

1. 安装依赖
```bash
npm run install:all
```

2. 配置环境变量
```bash
cp server/.env.example server/.env
# 编辑 server/.env 文件，配置数据库等信息
```

3. 启动数据库服务
```bash
docker-compose up -d postgres redis minio mosquitto
```

4. 运行数据库迁移
```bash
cd server
npm run db:migrate
npm run db:seed
```

5. 启动开发服务器
```bash
npm run dev
```

## 默认账户

系统初始化后会创建以下默认账户：

| 角色 | 工号 | 密码 | 说明 |
|------|------|------|------|
| 超级管理员 | admin | admin123456 | 系统管理员 |
| 站点管理员 | 2001 | admin123456 | 张三 |
| 巡检员 | 2002 | admin123456 | 李四 |
| 巡检员 | 2003 | admin123456 | 王五 |
| 查看者 | 2004 | admin123456 | 赵六 |

## API 文档

启动服务后，可以访问以下端点查看API信息：
- API概览: http://localhost:3000/api
- 健康检查: http://localhost:3000/health

主要API端点：
- `/api/auth/*` - 认证相关
- `/api/users/*` - 用户管理
- `/api/devices/*` - 设备管理
- `/api/sites/*` - 站址管理
- `/api/templates/*` - 模板管理
- `/api/tasks/*` - 任务管理
- `/api/upload/*` - 文件上传
- `/api/statistics/*` - 统计分析

## 开发状态

### ✅ 已完成
- [x] 项目基础架构搭建
- [x] 数据库设计和迁移
- [x] 用户认证和权限管理
- [x] 基础API框架
- [x] 前端项目初始化
- [x] Docker容器化配置

### 🚧 开发中
- [ ] 设备管理功能
- [ ] 站址管理功能
- [ ] 巡检模板配置
- [ ] 任务管理功能

### 📋 待开发
- [ ] 数据处理与通信接口
- [ ] 统计分析功能
- [ ] 文件上传和管理
- [ ] 实时监控功能
- [ ] 系统配置管理

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 项目Issues: [GitHub Issues](https://github.com/your-repo/issues)
- 邮箱: <EMAIL>
