version: '3.8'

services:
  # PostgreSQL 数据库
  postgres:
    image: postgres:15-alpine
    container_name: inspection_postgres
    environment:
      POSTGRES_DB: inspection_system
      POSTGRES_USER: admin
      POSTGRES_PASSWORD: admin123
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./server/database/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - inspection_network

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: inspection_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - inspection_network

  # MinIO 对象存储
  minio:
    image: minio/minio:latest
    container_name: inspection_minio
    environment:
      MINIO_ROOT_USER: admin
      MINIO_ROOT_PASSWORD: admin123456
    ports:
      - "9000:9000"
      - "9001:9001"
    volumes:
      - minio_data:/data
    command: server /data --console-address ":9001"
    networks:
      - inspection_network

  # MQTT Broker
  mosquitto:
    image: eclipse-mosquitto:2
    container_name: inspection_mqtt
    ports:
      - "1883:1883"
      - "9883:9883"
    volumes:
      - ./server/config/mosquitto.conf:/mosquitto/config/mosquitto.conf
      - mosquitto_data:/mosquitto/data
      - mosquitto_logs:/mosquitto/log
    networks:
      - inspection_network

  # 后端服务
  server:
    build:
      context: ./server
      dockerfile: Dockerfile
    container_name: inspection_server
    environment:
      NODE_ENV: production
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: inspection_system
      DB_USER: admin
      DB_PASSWORD: admin123
      REDIS_HOST: redis
      REDIS_PORT: 6379
      MINIO_ENDPOINT: minio
      MINIO_PORT: 9000
      MINIO_ACCESS_KEY: admin
      MINIO_SECRET_KEY: admin123456
      MQTT_HOST: mosquitto
      MQTT_PORT: 1883
    ports:
      - "3000:3000"
    depends_on:
      - postgres
      - redis
      - minio
      - mosquitto
    networks:
      - inspection_network

  # 前端服务
  client:
    build:
      context: ./client
      dockerfile: Dockerfile
    container_name: inspection_client
    ports:
      - "80:80"
    depends_on:
      - server
    networks:
      - inspection_network

volumes:
  postgres_data:
  redis_data:
  minio_data:
  mosquitto_data:
  mosquitto_logs:

networks:
  inspection_network:
    driver: bridge
